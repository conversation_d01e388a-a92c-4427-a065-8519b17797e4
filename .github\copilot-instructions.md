This repository holds multiple different parts of the application.
The backend is located in `host` and is a rust based project. Its statefull, it sends ArtNet, it provides an api, it connects to a mysql database.
The Frontend `interface` is a svelte project. The main feature is `blockly`
`modules` holds firmware for different keyboards.

## Development Flow

### Backend

- Run everything in the `host` directory or provide the argument `--manifest-path`
- Build: `cargo build`
- Check for correctness when iterating: `cargo check`
- Check for correctness when committing : `cargo clippy`
- Run: `cargo run`

### Frontend

- Run everything the `interface` directory
- Ignore any tauri related files and directorys
- Run: `npm run dev`
- Only format the code you changed

### Database

To test most features, a database is required.
Refer to the `host/scripts/setup.sh` file for reference.

### E2E testing

- We test via cypress in the root directory
- To run the tests, do `npm run cy:run`

## Key Guidelines
1. Always ensure, when the backend needs to aquire locks for shared state, it always locks in the following order: `db_handler`, `dmx_renderer`, `input_parser`, `dashboard` (This only applies when multiple locks need to be aquired simultaneously). Also make sure, that the locks are as shortly held as possible without cloning the state.
2. Follow best practices and ideomatic patterns
3. Maintain existing code structure and organization
4. Focus on your task and ignore existing TODO comments or other iprovements you see while reading the code
5. Always make sure, that clippy, cypress and the frontend typecheck do not output any warnings or errors
6. Never construct unit tests or run tests with cargo
7. Errors should never `throw` or `unwrap`, but display a toast message instead. Propagate errors as much as possible
8. Never worry about backwards compatibility
