import { type BlueprintFileDescriptor } from '../../../interface/src/lib/types/bindings/BlueprintFileDescriptor'

export function createAndEditBlueprint(blueprint: BlueprintFileDescriptor) {
    cy.contains('Blueprint Builder').click()
    cy.url().should('include', '/blueprintBuilder')

    cy.get('button[id="blueprintCreate"]').click()

    cy.get('button[id="blueprintSelect"]').click()
    cy.contains('new').click()

    cy.get('label')
        .contains('Blueprint Name')
        .closest('div')
        .find('input')
        .clear()
        .type(blueprint.name)

    addBlueprintProperties(blueprint)

    cy.get('button[id="blueprintSave"]').click()

    editBlueprintProperties()

    cy.get('button[id="blueprintSave"]').click()
}

function addBlueprintProperties(blueprint: BlueprintFileDescriptor) {
    blueprint.properties.forEach((property) => {
        if ("UnimplementedChannel" in property) {
            cy
                .get('select[name="Add Property to Blueprint"]')
                .select(property.UnimplementedChannel[0])
        }
    })
}

function editBlueprintProperties() {
    // Edit properties based on the blueprint object values
    // For now, this is a placeholder as the exact UI interaction depends on the blueprint properties
}

export function assertBlueprintAfterReload(blueprint: BlueprintFileDescriptor) {
    cy.reload()
    cy.wait(1000)
    cy.visit('/networking')
    cy.contains('127.0.0.1').click()

    // Navigate back to blueprint builder and select the blueprint
    cy.contains('Blueprint Builder').click()
    cy.url().should('include', '/blueprintBuilder')

    cy.get('button[id="blueprintSelect"]').click()
    cy.contains(blueprint.name).click()

    // Assert blueprint name persisted
    cy.get('label')
        .contains('Blueprint Name')
        .parent()
        .find('input')
        .should('have.value', blueprint.name)

    // Assert that the present properties equal that of the given blueprint object
    blueprint.properties.forEach((property) => {
        if ("UnimplementedChannel" in property) {
            cy.get('body').should('contain', property.UnimplementedChannel[0])
        }
    })
}
