import { createFixtureWithChannels, editAndAssertFixture } from './fixtureWorkflow'
import { createAndEditBlueprint, assertBlueprintAfterReload } from './blueprintWorkflow'
import { type DmxFixtureFileDescriptor } from '../../interface/src/lib/types/bindings/DmxFixtureFileDescriptor'
import { type BlueprintFileDescriptor } from '../../interface/src/lib/types/bindings/BlueprintFileDescriptor'

describe('Database seeder', () => {
    beforeEach(() => {
        cy.visit('/networking')
        cy.contains('127.0.0.1').click()
    })

    it('seeds shows', () => {
        cy.contains('Shows').click()
        cy.get('body').then(($body) => {
            if ($body.text().includes('cy:test')) {
                cy.contains('cy:test').click()
            }
            else {
                cy.get('label').contains('New name').closest('div').find('input').type('mySeededShow')
                cy.get('button[id="createShow"]').click()
            }
        })
    })

    it('seeds fixtures', () => {
        const fixture: DmxFixtureFileDescriptor = {
            id: null,
            dmx_address: null,
            dmx_universe: 1,
            name: "tst-1",
            fixturetype: "tst-tp-1",
            movement_channels: {
                pan: { channel: 4 },
                tilt: { channel: 5 }
            },
            stage_coordinates: [100, 200],
            footprint_size: 7,
            unimplemented_channels: [{
                id: null,
                channel: 6,
                dimmable: false,
                name: "testchannel",
                inverted: false,
                default: 101,
                keypoints: [{
                    id: null,
                    name: "kp_1", value: 7
                },
                {
                    id: null,
                    name: "kp_2", value: 0
                },
                {
                    id: null,
                    name: "kp_3", value: 102
                },
                {
                    id: null,
                    name: "kp_4", value: 255
                },
                {
                    id: null,
                    name: "kp_5", value: 40
                }
                ]
            },
            {
                id: null,
                channel: 7,
                dimmable: true,
                name: "testchannel2",
                inverted: true,
                default: 102,
                keypoints: []
            }],
            color: {
                color: {

                    red: 0,
                    green: 0,
                    blue: 0
                },
                channels: {
                    RgbChannels: {
                        r: 1,
                        g: 2,
                        b: 3
                    }
                }
            }
        }
        createFixtureWithChannels(fixture)
        cy.wait(1000)
        editAndAssertFixture(fixture)
        cy.contains('clear Form').click()

        const fixture2: DmxFixtureFileDescriptor = {
            id: null,
            dmx_address: null,
            dmx_universe: 1,
            name: "tst-2",
            fixturetype: "tst-tp-2",
            movement_channels: {
                pan: { channel: 512 },
                tilt: { channel: 17 }
            },
            stage_coordinates: [100, 200],
            footprint_size: 512,
            unimplemented_channels: [],
            color: {
                color: {

                    red: 0,
                    green: 0,
                    blue: 0
                },
                channels: {
                    HsvChannels: {
                        h: 1,
                        s: 2,
                        v: 3
                    }
                }
            }
        }
        createFixtureWithChannels(fixture2)
        cy.wait(1000)
        editAndAssertFixture(fixture2)
    })

    it('seeds blueprints', () => {
        let blueprint: BlueprintFileDescriptor = {
            id: null,
            name: 'bp-tst-1',
            properties: [],
            registered_fixture_delays: [],
            requires_user_action_reason: ""
        };

        createAndEditBlueprint(blueprint)
        cy.reload()
        assertBlueprintAfterReload(blueprint)
    })

    // it('should create snippet, set serial module key to 1, attach all blockly toolbox blocks, save, reload and assert positions', () => {
    //     createSnippetWithBlocklyBlocks()
    //     cy.reload()
    //     assertBlockPositions()
    // })

    // it('should create snippet with specific blocks, trigger it on online interface and assert ArtNet universe', () => {
    //     createSnippetWithSpecificBlocks()
    //     triggerSnippetAndAssertArtNet()
    // })
})
