import { DmxFixtureFileDescriptor } from "../../interface/src/lib/types/bindings/DmxFixtureFileDescriptor"

export function createFixtureWithChannels(fixture: DmxFixtureFileDescriptor) {
    cy.contains('Create Fixture').click()
    cy.url().should('include', '/createFixture')

    // Test all basic fixture fields
    cy.get('input').then(($inputs) => {
        cy.get('label')
            .contains('Fixturetype')
            .parent()
            .find('input')
            .clear()
            .type(fixture.fixturetype)

        cy.get('label')
            .contains('name')
            .parent()
            .find('input')
            .clear()
            .type(fixture.name)

        cy.get('label')
            .contains('Footprintsize')
            .parent()
            .find('input')
            .clear()
            .type(fixture.footprint_size.toString())
    })

    // Test amount to create field (if not editing)
    if (fixture.id === null) {
        cy.get('label')
            .contains('Amount to create')
            .parent()
            .find('input')
            .then($input => {
                if ($input.length > 0) {
                    cy.wrap($input).clear().type('1')
                }
            })
    }

    // Test color channel functionality - handle all color types
    if (fixture.color && fixture.color.channels) {
        if ("RgbChannels" in fixture.color.channels) {
            cy.get('select').select('RgbChannels')

            cy.get('button[id="red-channel"]').click()
            cy.get('label')
                .contains('(R)ed channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.RgbChannels.r.toString())

            cy.get('button[id="green-channel"]').click()
            cy.get('label')
                .contains('(G)reen channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.RgbChannels.g.toString())

            cy.get('button[id="blue-channel"]').click()
            cy.get('label')
                .contains('(B)lue channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.RgbChannels.b.toString())
        } else if ("CmyChannels" in fixture.color.channels) {
            cy.get('select').select('CmyChannels')

            cy.get('button[id="cyan-channel"]').click()
            cy.get('label')
                .contains('(C)yan channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.CmyChannels.c.toString())

            cy.get('button[id="magenta-channel"]').click()
            cy.get('label')
                .contains('(M)agenta channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.CmyChannels.m.toString())

            cy.get('button[id="yellow-channel"]').click()
            cy.get('label')
                .contains('(Y)ellow channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.CmyChannels.y.toString())
        } else if ("HsvChannels" in fixture.color.channels) {
            cy.get('select').select('HsvChannels')

            cy.get('button[id="hue-channel"]').click()
            cy.get('label')
                .contains('(H)ue channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.HsvChannels.h.toString())

            cy.get('button[id="saturation-channel"]').click()
            cy.get('label')
                .contains('(S)aturation channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.HsvChannels.s.toString())

            cy.get('button[id="value-channel"]').click()
            cy.get('label')
                .contains('(V)alue channel')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.HsvChannels.v.toString())
        } else if ("XyYChannels" in fixture.color.channels) {
            cy.get('select').select('XyzChannels')

            cy.get('button[id="x-channel"]').click()
            cy.get('label')
                .contains('RGB curves channel (X)')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.XyYChannels.x?.toString() || '1')

            cy.get('button[id="y-channel"]').click()
            cy.get('label')
                .contains('Luminance channel (Y)')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.XyYChannels.y?.toString() || '2')

            cy.get('button[id="z-channel"]').click()
            cy.get('label')
                .contains('~Blue channel (Z)')
                .parent()
                .find('input')
                .type("{selectall}")
                .type(fixture.color.channels.XyYChannels.yy?.toString() || '3')
        } else if ("ColorWheel" in fixture.color.channels) {
            cy.get('select').select('ColorWheel')

            const colorWheel = fixture.color.channels.ColorWheel
            cy.get('label').contains('channel').parent().find('input').clear().type(colorWheel.channel.toString())
            cy.get('label').contains('red').parent().find('input').clear().type(colorWheel.red.toString())
            cy.get('label').contains('green').parent().find('input').clear().type(colorWheel.green.toString())
            cy.get('label').contains('blue').parent().find('input').clear().type(colorWheel.blue.toString())
            cy.get('label').contains('light_blue').parent().find('input').clear().type(colorWheel.light_blue.toString())
            cy.get('label').contains('purple').parent().find('input').clear().type(colorWheel.purple.toString())
            cy.get('label').contains('pink').parent().find('input').clear().type(colorWheel.pink.toString())
            cy.get('label').contains('orange').parent().find('input').clear().type(colorWheel.orange.toString())
            cy.get('label').contains('yellow').parent().find('input').clear().type(colorWheel.yellow.toString())
            cy.get('label').contains('white').parent().find('input').clear().type(colorWheel.white.toString())
        }
    }

    // Test movement channels (pan/tilt)
    if (fixture.movement_channels) {
        cy.get('button[id="add-movement-channels"]').click()

        cy.get('label')
            .contains('Pan')
            .parent()
            .find('input')
            .clear()
            .type(fixture.movement_channels.pan.channel.toString())

        cy.get('label')
            .contains('Tilt')
            .parent()
            .find('input')
            .clear()
            .type(fixture.movement_channels.tilt.channel.toString())
    }

    // Test unimplemented channels with all fields
    fixture.unimplemented_channels.forEach((unimplemented_channel, index) => {
        cy.get('button[id="addUnimplementedChannel"]').click()

        // Test all unimplemented channel fields
        cy.get(`div[id="unimplemented-channel-${index}"]`).find('label').contains('Name').last().parent().find('input').clear().type(unimplemented_channel.name)
        cy.get(`div[id="unimplemented-channel-${index}"]`).find('label').contains('Channel').last().parent().find('input').type("{selectall}").type(unimplemented_channel.channel.toString())
        cy.get(`div[id="unimplemented-channel-${index}"]`).find('label').contains('Default').last().parent().find('input').clear().type(unimplemented_channel.default.toString())

        // Test checkbox fields
        //if (unimplemented_channel.dimmable) {
        //   cy.get('input[type="checkbox"]').eq(-2).check()
        //}
        //if (unimplemented_channel.inverted) {
        //    cy.get('input[type="checkbox"]').last().check()
        //}

        // Test keypoints
        unimplemented_channel.keypoints.forEach((keypoint, index) => {
            cy.get('button[id="add-keypoint"]').last().click()

            cy.get(`div[id=keypoint-${index}]`).find('label[for="Name"]').last().parent().find('input').clear().type(keypoint.name)
            cy.get(`div[id=keypoint-${index}]`).find('label[for="Value"]').last().parent().find('input').type("{selectall}").type(keypoint.value.toString())
        })
    })

    cy.get('button[id="edit-fixture"]').click()
}

export function editAndAssertFixture(fixture: DmxFixtureFileDescriptor) {
    cy.get('td', { timeout: 10000 }).contains(fixture.name).closest('tr').find('button[id="load-fixture-into-form"]').click()

    // Assert all basic fixture fields
    cy.get('label')
        .contains('Fixturetype')
        .parent()
        .find('input')
        .should('have.value', fixture.fixturetype)

    cy.get('label')
        .contains('Footprintsize')
        .parent()
        .find('input')
        .should('have.value', fixture.footprint_size.toString())

    // Assert color channel fields for all types
    if (fixture.color && fixture.color.channels) {
        if ("RgbChannels" in fixture.color.channels) {
            cy.get('label')
                .contains('(R)ed channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.RgbChannels.r.toString())

            cy.get('label')
                .contains('(G)reen channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.RgbChannels.g.toString())

            cy.get('label')
                .contains('(B)lue channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.RgbChannels.b.toString())
        } else if ("CmyChannels" in fixture.color.channels) {
            cy.get('label')
                .contains('(C)yan channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.CmyChannels.c.toString())

            cy.get('label')
                .contains('(M)agenta channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.CmyChannels.m.toString())

            cy.get('label')
                .contains('(Y)ellow channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.CmyChannels.y.toString())
        } else if ("HsvChannels" in fixture.color.channels) {
            cy.get('label')
                .contains('(H)ue channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.HsvChannels.h.toString())

            cy.get('label')
                .contains('(S)aturation channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.HsvChannels.s.toString())

            cy.get('label')
                .contains('(V)alue channel')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.HsvChannels.v.toString())
        } else if ("XyYChannels" in fixture.color.channels) {
            cy.get('label')
                .contains('RGB curves channel (X)')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.XyYChannels.x?.toString() || '1')

            cy.get('label')
                .contains('Luminance channel (Y)')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.XyYChannels.y?.toString() || '2')

            cy.get('label')
                .contains('~Blue channel (Z)')
                .parent()
                .find('input')
                .should('have.value', fixture.color.channels.XyYChannels.yy?.toString() || '3')
        } else if ("ColorWheel" in fixture.color.channels) {
            const colorWheel = fixture.color.channels.ColorWheel
            cy.get('label').contains('channel').parent().find('input').should('have.value', colorWheel.channel.toString())
            cy.get('label').contains('red').parent().find('input').should('have.value', colorWheel.red.toString())
            cy.get('label').contains('green').parent().find('input').should('have.value', colorWheel.green.toString())
            cy.get('label').contains('blue').parent().find('input').should('have.value', colorWheel.blue.toString())
            cy.get('label').contains('light_blue').parent().find('input').should('have.value', colorWheel.light_blue.toString())
            cy.get('label').contains('purple').parent().find('input').should('have.value', colorWheel.purple.toString())
            cy.get('label').contains('pink').parent().find('input').should('have.value', colorWheel.pink.toString())
            cy.get('label').contains('orange').parent().find('input').should('have.value', colorWheel.orange.toString())
            cy.get('label').contains('yellow').parent().find('input').should('have.value', colorWheel.yellow.toString())
            cy.get('label').contains('white').parent().find('input').should('have.value', colorWheel.white.toString())
        }
    }

    // Assert movement channels
    if (fixture.movement_channels) {
        cy.get('button[id="add-movement-channels"]').should('not.exist')

        cy.get('label')
            .contains('Pan')
            .parent()
            .find('input')
            .should('have.value', fixture.movement_channels.pan.channel.toString())

        cy.get('label')
            .contains('Tilt')
            .parent()
            .find('input')
            .should('have.value', fixture.movement_channels.tilt.channel.toString())
    } else {
        cy.get('button[id="add-movement-channels"]').should('exist')
    }

    // Assert all unimplemented channels and their fields
    fixture.unimplemented_channels.forEach((unimplemented_channel, channelIndex) => {
        cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).contains('Name').parent().find('input').should('have.value', unimplemented_channel.name)
        cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).contains('Channel').parent().find('input').should('have.value', unimplemented_channel.channel.toString())
        cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).contains('Default').parent().find('input').should('have.value', unimplemented_channel.default.toString())

        // Assert checkbox states
        // TODO: reenable this
        //if (unimplemented_channel.dimmable) {
        //    cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).find('input[type="checkbox"]').parent().find('input[type="checkbox"]').should('be.checked')
        //} else {
        //    cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).find('p').contains('Effected by "master dimmer"').parent().find('input[type="checkbox"]').should('not.be.checked')
        //}

        //if (unimplemented_channel.inverted) {
        //    cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).find('p').contains('Inverted').parent().find('input[type="checkbox"]').should('be.checked')
        //} else {
        //    cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).find('p').contains('Inverted').parent().find('input[type="checkbox"]').should('not.be.checked')
        //}

        // Assert keypoints
        unimplemented_channel.keypoints.forEach((keypoint, keypointIndex) => {
            cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).find(`div[id="keypoint-${keypointIndex}"]`)
                .find('label').contains('Name')
                .parent()
                .find('input')
                .should('have.value', keypoint.name)

            cy.get(`div[id="unimplemented-channel-${channelIndex}"]`).find(`div[id="keypoint-${keypointIndex}"]`)
                .find('label').contains('Value')
                .parent()
                .find('input')
                .should('have.value', keypoint.value.toString())
        })
    })
}
