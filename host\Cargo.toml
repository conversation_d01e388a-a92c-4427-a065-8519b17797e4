[package]
name = "host"
version = "0.0.0" # This will be set on each release by the pipeline
edition = "2021"

[dependencies]
# Serde for serializing and deserializing
serde = { version = "1.0.182", features = ["derive"] }
serde_json = "1.0.104"
# Gdtf-parser is used to parse gdtf files
gdtf-parser = { path = "gdtf_parser" }
# Colored to prettify the messages from the logging crate
colored = "2"
# Tokio allows us to have rest apis, also used for axum
tokio = { version = "1.29.1", features = ["full"] }
# Axum, futures-util, async-stream and Tower-http handle the rest-api
axum = { version = "0.6.2", features = ['multipart', 'ws', 'tokio'] }
futures-util = "0.3.30"
async-stream = "0.3.5"
tower-http = { version = "0.4.3", features = ['cors'] }
# This generates typescript types for the rest-api
ts-rs = "7"
# Provides the calculations for fixture propperties
splines = { version = "4.3.0", features = ["serde"] }
# Communicate with the RWmoules
serialport = "4.3.0"
# Selfmade utils
map_to_range = "0.2.0"
rwm_package_definitions = { path = "../modules/rwm_package_definitions" }
# Generate random numbers
rand = "0.8.5"
# Database
mysql = "25.0.1"
refinery = { version = "0.8.16", features = ["mysql"]}
# Audio processing
cpal = "0.16.0"
reqwest = { version = "0.12.22", features = ["json"] }

[profile.dev]
lto = "off"
debug = "line-tables-only"

[profile.release]
strip = "symbols"
lto = "fat"

[lints.clippy]
pedantic = { level = "warn", priority = -1 }
perf = { level = "warn", priority = -1 }
style = { level = "warn", priority = -1 }
nursery = { level = "warn", priority = -1 }
arithmetic_side_effects = "warn"
clone_on_ref_ptr = "warn"
expect_used = "warn"
float_cmp_const = "warn"
indexing_slicing = "warn"
panic = "warn"
string_add = "warn"
string_to_string = "warn"
todo = "warn"
unimplemented = "warn"
unreachable = "warn"
unwrap_used = "warn"
wildcard_enum_match_arm = "warn"
module_name_repetitions = "allow"
assigning_clones = "allow"
doc_lazy_continuation = "allow"
missing_errors_doc = "allow"
modulo_arithmetic = "warn"
as_conversions = "warn"
std_instead_of_core = "warn"
std_instead_of_alloc = "warn"
# float_arithmetic = "warn"
