use crate::audio_analysis::traits::{AnalysisResult, AudioAnalyzer};
use std::time::Instant;

pub struct LevelAnalyzer {
    enabled: bool,
    current_rms: f32,
    current_peak: f32,
}

impl LevelAnalyzer {
    #[must_use]
    pub const fn new() -> Self {
        Self {
            enabled: true,
            current_rms: 0.0,
            current_peak: 0.0,
        }
    }
}

impl AudioAnalyzer for LevelAnalyzer {
    #[allow(clippy::as_conversions, clippy::cast_precision_loss)]
    fn process_samples(
        &mut self,
        samples: &[f32],
        _sample_rate: u32,
        _timestamp: Instant,
    ) {
        if samples.is_empty() {
            return;
        }

        // Calculate RMS (Root Mean Square)
        let sum_squares: f32 = samples.iter().map(|&s| s * s).sum();
        self.current_rms = (sum_squares / samples.len() as f32).sqrt();

        // Calculate peak level
        self.current_peak =
            samples.iter().map(|&s| s.abs()).fold(0.0_f32, f32::max);
    }

    fn name(&self) -> &'static str {
        "LevelAnalyzer"
    }

    fn is_enabled(&self) -> bool {
        self.enabled
    }

    fn set_enabled(&mut self, enabled: bool) {
        self.enabled = enabled;
    }

    fn get_results(&self) -> Option<AnalysisResult> {
        Some(AnalysisResult::Level {
            rms: self.current_rms,
            peak: self.current_peak,
        })
    }

    fn reset(&mut self) {
        self.current_rms = 0.0;
        self.current_peak = 0.0;
    }
}

impl Default for LevelAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
