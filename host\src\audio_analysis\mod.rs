use alloc::sync::Arc;
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use cpal::{Device, SampleFormat, Stream, StreamConfig};
use tokio::sync::Mutex;

pub mod beat_detector;
pub mod level_analyzer;
mod traits;

use crate::logging;

#[derive(Debug, <PERSON>lone)]
pub struct AudioConfig {
    pub sample_rate: usize,
    pub channels: u16,
    pub buffer_size: usize,
}

impl Default for AudioConfig {
    fn default() -> Self {
        Self {
            sample_rate: 44100,
            channels: 1,
            buffer_size: 1024,
        }
    }
}

pub struct AudioProcessor {
    config: AudioConfig,
    pub analyzers: Vec<Box<dyn traits::AudioAnalyzer>>,
    sample_buffer: (Vec<f32>, usize),
}

#[derive(Debug)]
pub enum AudioError {
    NoInputDevice,
    ConfigurationNotSupported,
    UnsupportedFormat,
    StreamError,
    NotInitialized,
    ProcessorLocked,
}

impl AudioProcessor {
    #[must_use]
    pub fn new(config: AudioConfig) -> Self {
        let buffer_size: usize = config.sample_rate;
        let sample_buffer = vec![0.0; buffer_size];

        Self {
            analyzers: Vec::new(),
            config,
            sample_buffer: (sample_buffer, 0),
        }
    }

    pub fn spawn_audio_analysis(processor: Arc<Mutex<Self>>) {
        tokio::spawn(async move {
            if Self::setup_audio_capture(processor).is_err() {
                logging::log(
                    "Audio capture initialization failed".to_string(),
                    logging::LogLevel::Warning,
                    true,
                );
            }
        });
    }

    fn setup_audio_capture(
        processor: Arc<Mutex<Self>>,
    ) -> Result<(), AudioError> {
        let host = cpal::default_host();

        let device = host
            .default_input_device()
            .ok_or(AudioError::NoInputDevice)?;

        let config = device
            .default_input_config()
            .map_err(|_| AudioError::ConfigurationNotSupported)?;

        #[allow(clippy::wildcard_enum_match_arm)]
        let stream = match config.sample_format() {
            SampleFormat::F32 => Self::build_input_stream::<f32>(
                &device,
                &config.into(),
                processor,
            )?,
            SampleFormat::I16 => Self::build_input_stream::<i16>(
                &device,
                &config.into(),
                processor,
            )?,
            SampleFormat::U16 => Self::build_input_stream::<u16>(
                &device,
                &config.into(),
                processor,
            )?,
            _ => return Err(AudioError::UnsupportedFormat),
        };

        stream.play().map_err(|_| AudioError::StreamError)?;

        logging::log(
            format!(
                "Audio capture started on device {}",
                device.name().unwrap_or_else(|_| "Unknown".to_string())
            ),
            logging::LogLevel::Info,
            false,
        );

        std::thread::park();
        Ok(())
    }

    /// Build input stream for a specific sample type
    fn build_input_stream<T>(
        device: &Device,
        config: &StreamConfig,
        processor: Arc<Mutex<Self>>,
    ) -> Result<Stream, AudioError>
    where
        T: cpal::Sample + cpal::SizedSample + Send + 'static,
        f32: From<T>,
    {
        let stream = device
            .build_input_stream(
                config,
                move |data: &[T], _: &cpal::InputCallbackInfo| {
                    let samples: Vec<f32> =
                        data.iter().map(|&sample| f32::from(sample)).collect();

                    if let Ok(mut proc) = processor.try_lock() {
                        proc.store_samples(&samples);
                        proc.process_stored_samples();
                    }
                },
                |err| {
                    logging::log(
                        format!("Audio stream error: {err}"),
                        logging::LogLevel::Warning,
                        true,
                    );
                },
                None,
            )
            .map_err(|_| AudioError::StreamError)?;

        Ok(stream)
    }

    pub fn store_samples(&mut self, samples: &[f32]) {
        for sample in samples {
            if self.sample_buffer.1 >= self.config.buffer_size {
                self.sample_buffer.1 = 0;
            } else {
                self.sample_buffer.1 = self.sample_buffer.1.saturating_add(1);
            }
            if let Some(buffer_sample) = self
                .sample_buffer
                .0
                .get_mut(self.sample_buffer.1.wrapping_sub(1))
            {
                *buffer_sample = *sample;
            }
        }
    }

    fn process_stored_samples(&mut self) {
        if self.sample_buffer.1 == 0 {
            return; // No samples to process
        }

        for analyzer in &mut self.analyzers {
            analyzer.process_samples(
                self.sample_buffer
                    .0
                    .get(..self.sample_buffer.1.wrapping_sub(1))
                    .unwrap_or(&[]),
                0,
                std::time::Instant::now(),
            );
        }
    }

    /// Reset all analyzers
    pub fn reset_analyzers(&mut self) {
        for analyzer in &mut self.analyzers {
            analyzer.reset();
        }
        self.sample_buffer.0 = vec![0.0; self.config.buffer_size];
        self.sample_buffer.1 = 0;
    }
}

impl core::fmt::Display for AudioError {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        match self {
            Self::NoInputDevice => {
                write!(f, "No audio input device available")
            }
            Self::ConfigurationNotSupported => {
                write!(f, "Audio configuration not supported")
            }
            Self::UnsupportedFormat => {
                write!(f, "Audio format not supported")
            }
            Self::StreamError => write!(f, "Audio stream error"),
            Self::NotInitialized => {
                write!(f, "Audio system not initialized")
            }
            Self::ProcessorLocked => {
                write!(f, "Audio processor currently locked")
            }
        }
    }
}

impl core::error::Error for AudioError {}
