pub trait AudioAnalyzer: Send {
    /// Process a buffer of audio samples
    ///
    /// # Arguments
    /// * `samples` - Audio samples as f32 values in the range [-1.0, 1.0]
    /// * `sample_rate` - Sample rate in Hz
    /// * `timestamp` - Timestamp when the samples were captured
    fn process_samples(
        &mut self,
        samples: &[f32],
        sample_rate: u32,
        timestamp: std::time::Instant,
    );

    /// Get the name of this analyzer (for logging and debugging)
    fn name(&self) -> &str;

    /// Check if this analyzer is currently active/enabled
    fn is_enabled(&self) -> bool;

    /// Enable or disable this analyzer
    fn set_enabled(&mut self, enabled: bool);

    /// Get analysis results as a generic value
    /// This can be used to retrieve analysis results for use by other parts of the system
    fn get_results(&self) -> Option<AnalysisResult>;

    /// Reset the analyzer state (useful for clearing accumulated data)
    fn reset(&mut self);
}

#[derive(Debug, <PERSON><PERSON>)]
pub enum AnalysisResult {
    /// Audio level/volume information
    Level {
        /// Current RMS level (0.0 to 1.0)
        rms: f32,
        /// Peak level (0.0 to 1.0)
        peak: f32,
    },
    /// Beat detection result
    Beat {
        /// Whether a beat was detected in this frame
        detected: bool,
        /// Confidence level (0.0 to 1.0)
        confidence: f32,
        /// Estimated BPM
        bpm: Option<f32>,
    },
}
