CREATE TABLE fixturegroups (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    show_id INT UNSIGNED,
    FOREI<PERSON>N KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE
);

CREATE TABLE fixturegroup_fixtures (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    fixturegroup_id INT UNSIGNED,
    fixture_id INT UNSIGNED,
    FOREIG<PERSON> KEY (fixturegroup_id) REFERENCES fixturegroups(id) ON DELETE CASCADE,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);
