use mysql::prelude::*;
use mysql::{serde, PooledConn};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

use crate::{dmx_renderer::DmxRenderer, logging};

use super::<PERSON>b<PERSON><PERSON><PERSON>;

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct ComposedPanTiltPosition {
    pub id: usize,
    pub name: String,
    pub fixture_positions: Vec<FixturePosition>,
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct FixturePosition {
    pub fixture_id: usize,
    pub pan: u8,
    pub tilt: u8,
}
impl FixturePosition {
    #[must_use]
    pub const fn new(fixture_id: usize, pan: u8, tilt: u8) -> Self {
        Self {
            fixture_id,
            pan,
            tilt,
        }
    }
    #[must_use]
    pub const fn pan(&self) -> u8 {
        self.pan
    }
    #[must_use]
    pub const fn tilt(&self) -> u8 {
        self.tilt
    }
    #[must_use]
    pub const fn fixture_id(&self) -> usize {
        self.fixture_id
    }
}

impl DbHandler {
    #[must_use]
    pub fn pan_tilt_positions(
        db_connection: &mut PooledConn,
    ) -> Vec<ComposedPanTiltPosition> {
        let mut result: Vec<ComposedPanTiltPosition> = vec![];
        db_connection.query_map("
           SELECT
                positions.id,
                positions.name,
                fx_positions.pan,
                fx_positions.tilt,
                fx_positions.fixture_id
            FROM pan_tilt_positions AS positions
            JOIN fixture_positions AS fx_positions ON fx_positions.pan_tilt_position_id = positions.id
            JOIN shows ON positions.show_id = shows.id
            WHERE shows.active",
        |(position_id, position_name, pan, tilt, fixture_id)| {
                match result.iter_mut().find(|pos| pos.id == position_id){
                    None => result.push(
                        ComposedPanTiltPosition {
                            id: position_id,
                            name: position_name,
                            fixture_positions: vec![
                                FixturePosition{
                                    fixture_id,
                                    pan,
                                    tilt,
                                }
                            ]
                        }
                    ),
                    Some(position) => {
                        position.fixture_positions.push(
                            FixturePosition {
                                fixture_id,
                                pan,
                                tilt,
                            }
                        );
                    }
                }
            }
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (Selecting all pan_tilt_position)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
        result
    }
    pub fn rename_pan_tilt_position(
        &mut self,
        position_id: usize,
        new_name: &str,
    ) {
        self.db_connection()
            .query_drop(format!(
            "UPDATE pan_tilt_positions SET name = '{new_name}' WHERE id = {position_id}"
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Renaming pan_tilt_position)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.positions =
            Self::pan_tilt_positions(&mut self.db_connection());
        self.publish_changes();
    }
    pub fn delete_pan_tilt_position(&mut self, position_id: usize) {
        self.db_connection()
            .query_drop(format!(
                "DELETE FROM pan_tilt_positions WHERE id = {position_id}"
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Deleting from pan_tilt_positions)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.positions =
            Self::pan_tilt_positions(&mut self.db_connection());
        self.publish_changes();
    }
    pub fn create_pan_tilt_position_from_values(
        &mut self,
        name: &str,
        fixture_positions: &Vec<FixturePosition>,
    ) {
        self.db_connection()
                .query_drop(format!(
                    "INSERT INTO pan_tilt_positions (name, show_id) VALUES ('{name}', (SELECT id FROM shows WHERE active))"
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Creating new pan_tilt_position)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        for position in fixture_positions {
            self.db_connection().query_drop(format!(
                    "INSERT INTO fixture_positions
                        (pan, tilt, fixture_id, pan_tilt_position_id)
                        VALUES ({}, {}, {}, (SELECT MAX(id) FROM pan_tilt_positions))",
                    position.pan, position.tilt, position.fixture_id
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Creating new fixture_positions)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        }
        self.active_show.positions =
            Self::pan_tilt_positions(&mut self.db_connection());
        self.publish_changes();
    }
    pub fn trigger_pan_tilt_position_creation(
        &mut self,
        dmx_renderer: &mut DmxRenderer,
        name: &str,
    ) {
        let pan_tilt_positions = dmx_renderer.capture_pan_tilt_positions();
        self.db_connection()
                .query_drop(format!(
                    "INSERT INTO pan_tilt_positions (name, show_id) VALUES ('{name}', (SELECT id FROM shows WHERE active))"
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Creating new pan_tilt_position)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        for position in &pan_tilt_positions {
            self.db_connection().query_drop(format!(
                    "INSERT INTO fixture_positions
                        (pan, tilt, fixture_id, pan_tilt_position_id)
                        VALUES ({}, {}, {}, (SELECT MAX(id) FROM pan_tilt_positions))",
                    position.pan, position.tilt, position.fixture_id
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Creating new fixture_positions)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        }
        self.active_show.positions =
            Self::pan_tilt_positions(&mut self.db_connection());
        self.publish_changes();
    }
    pub fn trigger_pan_tilt_position_update(
        &mut self,
        dmx_renderer: &mut DmxRenderer,
        id: usize,
    ) {
        let pan_tilt_positions = dmx_renderer.capture_pan_tilt_positions();
        self.db_connection()
            .query_drop(format!(
                "
                        DELETE fp FROM fixture_positions fp
                        JOIN pan_tilt_positions ptp
                        ON fp.pan_tilt_position_id = ptp.id
                        WHERE ptp.id = {id}
                    "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Deleting old fixture_positions)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        for position in &pan_tilt_positions {
            self.db_connection()
                .query_drop(format!(
                    "INSERT INTO fixture_positions
                    (pan, tilt, fixture_id, pan_tilt_position_id)
                    VALUES ({}, {}, {}, {id})",
                    position.pan, position.tilt, position.fixture_id
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Inserting new fixture_positions)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        }
        self.active_show.positions =
            Self::pan_tilt_positions(&mut self.db_connection());
        self.publish_changes();
    }
}
