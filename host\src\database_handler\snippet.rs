use mysql::{prelude::Queryable, PooledConn};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

use crate::{
    dmx_renderer::channel::{
        color_channels::RgbColor,
        unimplemented_channels::UnimplementedChannelSetter,
    },
    input_parser::structs::{
        AddToGroup, BlueprintToInstruction, ColorToInstruction, Comparison,
        ConditionalBlock, DelayByNotesInstruction, FixtureLoop, FunctionGetter,
        Instruction, InstructionValue, LoopStatement, MathOperator,
        PrintStatement, RemoveFromGroup, TimecodeToInstruction, VariableSetter,
    },
    logging,
};

use super::{
    directory_tree::{Directory, Identifiable, TreeItem},
    DbHandler,
};
use core::fmt;

#[derive(
    Default, PartialEq, Eq, Debug, Clone, Copy, Serialize, Deserialize, TS,
)]
#[ts(export)]
pub enum SnippetCategory {
    Startup,
    #[default]
    Key,
    Watcher,
    Callable,
}
impl fmt::Display for SnippetCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Startup => write!(f, "Startup"),
            Self::Key => write!(f, "Key"),
            Self::Watcher => write!(f, "Watcher"),
            Self::Callable => write!(f, "Callable"),
        }
    }
}

#[derive(Debug, Clone, Serialize, TS)]
#[ts(export)]
pub struct LibrarySnippet {
    pub category: SnippetCategory,
    pub name: String,
    pub do_not_use_instructions: bool,
    pub instructions: Vec<Instruction>,
}
#[derive(Default, Debug, Clone, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct Snippet {
    pub id: usize,
    pub category: SnippetCategory,
    pub serial_module_key: Option<u16>,
    pub name: String,
    pub do_not_use_instructions: bool,
    pub instructions: Vec<Instruction>,
    pub requires_user_action_reason: Option<String>,
}

impl Identifiable for Snippet {
    type Identifier = usize;

    fn identify(&self, identifier: Self::Identifier) -> bool {
        self.id == identifier
    }
}

fn saturate_snippet(
    db_connection: &mut PooledConn,
    snippet_id: usize,
) -> Vec<Instruction> {
    let mut instruction_ids: Vec<usize> = vec![];
    db_connection
        .query_map(
            format!(
                "
                    SELECT i.id
                    FROM instructions i
                    JOIN snippets s ON i.snippet_id = s.id
                    WHERE s.id = {snippet_id}
                ",
            ),
            |id| {
                instruction_ids.push(id);
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting instructions of snippet)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
    instruction_ids
        .iter()
        .map(|id| DbHandler::instruction(db_connection, *id))
        .collect()
}
fn saturate_result_dir_content(
    db_connection: &mut PooledConn,
    parent_dir_id: usize,
    directory: &mut Directory<Snippet>,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        s.id,
                        s.name,
                        s.category,
                        s.do_not_use_instructions,
                        s.serial_module_key,
                        s.requires_user_action_reason
                    FROM snippets AS s
                    JOIN snippet_dirs AS sd ON s.parent_dir_id = sd.id
                    WHERE s.parent_dir_id = {parent_dir_id}
                "
            ),
            |(
                id,
                name,
                db_category,
                do_not_use_instructions,
                serial_module_key,
                requires_user_action_reason,
            ): (
                usize,
                String,
                Option<String>,
                bool,
                Option<u16>,
                Option<String>,
            )| {
                let category =
                    db_category.map_or(SnippetCategory::Key, |db_category| {
                        match db_category.as_str() {
                            "Startup" => SnippetCategory::Startup,
                            "Watcher" => SnippetCategory::Watcher,
                            "Callable" => SnippetCategory::Callable,
                            _ /* | "Key" */ => SnippetCategory::Key,
                        }
                    });
                directory.content.push(TreeItem::Item(Snippet {
                    id,
                    category,
                    serial_module_key,
                    name,
                    do_not_use_instructions,
                    instructions: vec![],
                    requires_user_action_reason,
                }));
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!(
                    "{err:?}\n        (selecting child snippets of directory)"
                ),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

impl DbHandler {
    #[must_use]
    pub fn snippets_dir_for_active_show(
        db_connection: &mut PooledConn,
    ) -> Option<Directory<Snippet>> {
        db_connection
            .query_map(
                "
                    SELECT snippet_dirs.id
                    FROM snippet_dirs
                    JOIN shows on snippet_dirs.show_id = shows.id
                    WHERE shows.active
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (select snippet dir for active show)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .map(|id| Self::directory(db_connection, *id))
    }

    fn directory(
        db_connection: &mut PooledConn,
        id: usize,
    ) -> Directory<Snippet> {
        let mut result_dir_name = String::new();
        let mut result_dir = Directory {
            id,
            name: db_connection
                .query_map(
                    format!(
                        "
                            SELECT name
                            FROM snippet_dirs
                            WHERE id = {id}
                        "
                    ),
                    |name| name,
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (selecting child directories)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                })
                .first()
                .cloned()
                .unwrap_or_default(),
            content: db_connection
                .query_map(
                    format!(
                        "
                            SELECT c.id, p.name
                            FROM snippet_dirs AS c
                            JOIN snippet_dirs AS p ON c.parent_dir_id = p.id
                            WHERE p.id = {id}
                        "
                    ),
                    |(child_id, parent_name)| {
                        result_dir_name = parent_name;
                        child_id
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (selecting child directories)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                })
                .iter()
                .map(|dir_id| {
                    TreeItem::Directory(Self::directory(db_connection, *dir_id))
                })
                .collect(),
        };
        if !result_dir_name.is_empty() {
            result_dir.name = result_dir_name;
        }

        saturate_result_dir_content(db_connection, id, &mut result_dir);

        for tree_item in &mut result_dir.content {
            if let TreeItem::Item(ref mut snippet) = tree_item {
                snippet.instructions =
                    saturate_snippet(db_connection, snippet.id);
            }
        }
        result_dir
    }
    #[allow(clippy::too_many_lines)]
    fn instruction(db_connection: &mut PooledConn, id: usize) -> Instruction {
        let result_instruction = db_connection
            .query_map(
                format!(
                    "
                            SELECT
                                variant,
                                int_val,
                                str_val,
                                bool_val_1,
                                bool_val_2
                            FROM instructions
                            WHERE instructions.id = {id}
                            "
                ),
                |(variant, int_val, str_val, bool_val_1, bool_val_2): (
                    String,
                    usize,
                    String,
                    bool,
                    bool,
                )| {
                    match variant.as_str() {
                        "ColorTo" => Instruction::ColorTo(ColorToInstruction {
                            color: RgbColor::from_hex(&str_val),
                            fade_duration_beat_count: None,
                        }),
                        "StartRecording" => Instruction::StartRecording,
                        "StopRecording" => Instruction::StopRecording,
                        "ClearRecording" => Instruction::ClearRecording,
                        "ColorToRandom" => Instruction::ColorToRandom(None),
                        "SetBlueprintIntensity" => {
                            Instruction::SetBlueprintIntensity(None)
                        }
                        "SetSpeedOfBlueprints" => {
                            Instruction::SetSpeedOfBlueprints(None)
                        }
                        "ActivateAllFixtures" => {
                            Instruction::ActivateAllFixtures
                        }
                        "ActivateFixtureById" => {
                            Instruction::ActivateFixtureById(int_val)
                        }
                        "ActivateFixtureGroup" => {
                            Instruction::ActivateFixtureGroup(str_val)
                        }
                        "BlueprintTo" => {
                            Instruction::BlueprintTo(BlueprintToInstruction {
                                id: int_val,
                                oneshot: bool_val_1,
                                delay: bool_val_2,
                            })
                        }
                        "TimecodeTo" => {
                            Instruction::TimecodeTo(TimecodeToInstruction {
                                id: int_val,
                                index: 0,
                                play: true,
                            })
                        }
                        "SetBlueprintPositionIndexOffsetMode" => {
                            Instruction::SetBlueprintPositionIndexOffsetMode(
                                str_val.into(),
                            )
                        }
                        "ToggleQueueMode" => {
                            Instruction::ToggleQueueMode(str_val.into())
                        }
                        "UnimplementedChannelTo" => {
                            Instruction::UnimplementedChannelTo(
                                UnimplementedChannelSetter {
                                    name: str_val,
                                    value: None,
                                    fade_duration_beat_count: None,
                                },
                            )
                        }
                        "ClearBlueprint" => {
                            Instruction::ClearBlueprint(int_val)
                        }
                        "PanTo" => Instruction::PanTo(None),
                        "ExecuteCallableSnippet" => {
                            Instruction::ExecuteCallableSnippet(int_val)
                        }
                        "AddToPan" => Instruction::AddToPan(None),
                        "TiltTo" => Instruction::TiltTo(None),
                        "AddToTilt" => Instruction::AddToTilt(None),
                        "PositionTo" => Instruction::PositionTo(int_val),
                        "BpmTo" => Instruction::BpmTo(None),
                        "BpmModifierTo" => Instruction::BpmModifierTo(None),
                        "FixtureLoop" => {
                            Instruction::FixtureLoop(FixtureLoop {
                                fixtures: vec![],
                                instructions: vec![],
                            })
                        }
                        "CreateVariable" => {
                            Instruction::CreateVariable(str_val)
                        }
                        "SetVariable" => {
                            Instruction::SetVariable(VariableSetter {
                                name: str_val,
                                value: None,
                            })
                        }
                        "AddToGroup" => Instruction::AddToGroup(AddToGroup {
                            name: str_val,
                            fixtures: vec![],
                        }),
                        "RemoveFromGroup" => {
                            Instruction::RemoveFromGroup(RemoveFromGroup {
                                name: str_val,
                                fixtures: vec![],
                            })
                        }
                        "DelayByNotes" => {
                            Instruction::DelayByNotes(DelayByNotesInstruction {
                                note_type: str_val.into(),
                                count: None,
                            })
                        }
                        "DeselectAllFixtures" => {
                            Instruction::DeselectAllFixtures
                        }
                        "DimmerTo" => Instruction::DimmerTo(None),
                        "IfStatement" => Instruction::IfStatement(vec![]),
                        "LoopStatement" => {
                            Instruction::LoopStatement(LoopStatement {
                                comparison: Comparison {
                                    left_value: None,
                                    right_value: None,
                                    comparator: str_val.into(),
                                },
                                content: vec![],
                            })
                        }
                        "IfQueueAllowsContinue" => {
                            Instruction::IfQueueAllowsContinue(vec![])
                        }
                        "Print" => Instruction::Print(PrintStatement {
                            text: str_val,
                            value: None,
                        }),
                        _ => Instruction::Nop,
                    }
                },
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (selecting instruction from )"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        result_instruction.first().map_or(Instruction::Nop, |result_instruction| {
            match result_instruction {
                Instruction::SetBlueprintIntensity(_) => {
                    Instruction::SetBlueprintIntensity(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::DimmerTo(_) => {
                    Instruction::DimmerTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::SetSpeedOfBlueprints(_) => {
                    Instruction::SetSpeedOfBlueprints(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::PanTo(_) => {
                    Instruction::PanTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::AddToPan(_) => {
                    Instruction::AddToPan(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::TiltTo(_) => {
                    Instruction::TiltTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::AddToTilt(_) => {
                    Instruction::AddToTilt(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::BpmTo(_) => {
                    Instruction::BpmTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::BpmModifierTo(_) => {
                    Instruction::BpmModifierTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::DelayByNotes(delay_instruction) => {
                    let count = Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    );
                    Instruction::DelayByNotes(DelayByNotesInstruction {
                        note_type: delay_instruction.note_type,
                        count,
                    })
                }

                Instruction::FixtureLoop(_) => {
                    Instruction::FixtureLoop(FixtureLoop {
                        fixtures: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'fixtures'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting fixtures inside fx_loop)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        instructions: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'instructions'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting instructions inside fx_loop)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                    })
                }
                Instruction::SetVariable(sv) => {
                    Instruction::SetVariable(VariableSetter {
                        value: Self::instruction_value(
                            db_connection,
                            Some(id),
                            None,
                            None,
                        ),
                        name: sv.name.clone(),
                    })
                }
                Instruction::AddToGroup(atg) => {
                    Instruction::AddToGroup(AddToGroup {
                        fixtures: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'fixtures'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting fixtures inside AddToGroup)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        name: atg.name.clone(),
                    })
                }
                Instruction::RemoveFromGroup(rfg) => {
                    Instruction::RemoveFromGroup(RemoveFromGroup {
                        fixtures: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'fixtures'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting fixtures inside RemoveFromGroup)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        name: rfg.name.clone(),
                    })
                }
                Instruction::ColorTo(color_to) => {

                    // TODO: Remove db_query in favor of `instruction_value` returning an Option<...>
                    let iv_count = db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT count(iv.id)
                                    FROM instruction_values iv
                                    JOIN instructions i ON i.id = iv.instruction_id
                                    WHERE i.id = {id}
                                "
                            ),
                            |count: usize| count,
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Counting instruction_values by id)"
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                    Instruction::ColorTo(
                        ColorToInstruction {
                            color: color_to.color,
                            fade_duration_beat_count:
                                if iv_count.is_empty() || iv_count.first().is_some_and(|n| *n == 0) {
                                    None
                                } else {
                                    Self::instruction_value(db_connection, Some(id), None, None)
                                }
                        }
                    )
                }
                Instruction::ColorToRandom(_) => {
                    let iv_count = db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT count(iv.id)
                                    FROM instruction_values iv
                                    JOIN instructions i ON i.id = iv.instruction_id
                                    WHERE i.id = {id}
                                "
                            ),
                            |count: usize| count,
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Counting instruction_values by id)"
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                    Instruction::ColorToRandom(
                        if iv_count.is_empty() || iv_count.first().is_some_and(|n| *n == 0) {
                            None
                        }else {
                            Self::instruction_value(db_connection, Some(id), None, None)
                        }
                    )

                }
                Instruction::UnimplementedChannelTo(uct) => {
                    Instruction::UnimplementedChannelTo(
                        UnimplementedChannelSetter {
                            value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("value".to_owned()),
                            ),
                            name: uct.name.clone(),
                            fade_duration_beat_count: Self::instruction_value(db_connection, Some(id), None, Some("fade_duration".to_owned()))
                        },
                    )
                }
                Instruction::Print(print_instruction) => {
                    Instruction::Print(PrintStatement {
                        text: print_instruction.text.clone(),
                        value: Self::instruction_value(
                            db_connection,
                            Some(id),
                            None,
                            Some("value".to_owned())
                        ),

                    })
                }

                Instruction::StartRecording
                | Instruction::StopRecording
                | Instruction::ClearRecording
                | Instruction::DeselectAllFixtures
                | Instruction::ClearBlueprint(_)
                | Instruction::CreateVariable(_)
                | Instruction::PositionTo(_)
                | Instruction::ActivateAllFixtures
                | Instruction::ActivateFixtureById(_)
                | Instruction::ActivateFixtureGroup(_)
                | Instruction::BlueprintTo(_)
                | Instruction::TimecodeTo(_)
                | Instruction::SetBlueprintPositionIndexOffsetMode(_)
                | Instruction::ToggleQueueMode(_)
                | Instruction::ExecuteCallableSnippet(_)
                | Instruction::Nop => result_instruction.clone(),

                Instruction::IfStatement(_) => {
                    Instruction::IfStatement({
                        let mut conditional_block_ids: Vec<usize> = vec![];
                        db_connection.query_map(
                            format!(
                                "
                                    SELECT ci.id FROM instructions pi
                                    JOIN instructions ci ON pi.id = ci.parent_instruction_id
                                    WHERE pi.id = {id} AND ci.variant = 'ConditionalBlock'
                                "
                            ),
                            |id| conditional_block_ids.push(id)
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!("{err:?}\n        (selecting conditional_block_ids)"),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });

                        let mut saturated_conditional_blocks = vec![];

                        for conditional_block_id in conditional_block_ids {
                            let conditional_block_values: Vec<(usize, String)> = db_connection
                                .query_map(
                                    format!(
                                        "
                                                SELECT
                                                    int_val,
                                                    str_val
                                                FROM instructions
                                                WHERE instructions.id = {id}
                                                "
                                    ),
                                    |(int_val, str_val): ( usize, String,)| {
                                        (int_val, str_val)
                                    })
                                    .unwrap_or_else(|err| {
                                        logging::log(
                                            format!("{err:?}\n        (selecting conditional_blocks for if-statement)"),
                                            logging::LogLevel::DbError,
                                            true,
                                        );
                                        vec![]
                                    });

                            let Some(conditional_block_values) = conditional_block_values.first() else {
                                continue;
                            };

                            saturated_conditional_blocks.push((conditional_block_values.0, ConditionalBlock {
                                comparison: Comparison {
                                    left_value: Self::instruction_value(
                                                    db_connection,
                                                    Some(conditional_block_id),
                                                    None,
                                                    Some("left_value".to_owned())
                                                ),
                                    right_value: Self::instruction_value(
                                                    db_connection,
                                                    Some(conditional_block_id),
                                                    None,
                                                    Some("right_value".to_owned())
                                                ),
                                    comparator: conditional_block_values.1.clone().into(),
                                },
                                content: {
                                    let mut g_ids: Vec<usize> = vec![];
                                    db_connection
                                .query_map(
                                    format!(
                                        "
                                            SELECT ivv.id FROM instructions i
                                            JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                            WHERE i.id = {conditional_block_id} AND ivv.branch = 'content'
                                        "
                                    ),
                                    |id| {
                                        g_ids.push(id);
                                    },
                                )
                                .unwrap_or_else(|err| {
                                    logging::log(
                                        format!(
                                        "{err:?}\n        (selecting content inside conditional_blocks)"
                                    ),
                                    logging::LogLevel::DbError,
                                    true,
                                );
                                vec![]
                                });
                                    let mut result = vec![];
                                    for g_id in &g_ids {
                                        result.push(Self::instruction(
                                            db_connection,
                                            *g_id,
                                        ));
                                    }
                                    result
                                },
                                    }));
                                };


                                saturated_conditional_blocks.sort_by_key(|k| k.0);

                                saturated_conditional_blocks
                                            .iter()
                                            .map(|(_, conditional_block)| conditional_block)
                                            .cloned()
                                            .collect()
                            })
                }
                Instruction::LoopStatement(ls) => {
                    Instruction::LoopStatement(LoopStatement {
                        comparison: Comparison {
                            left_value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("left_value".to_owned()),
                            ),
                            right_value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("right_value".to_owned()),
                            ),
                            comparator: ls.comparison.comparator,
                        },
                        content: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'content'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting content inside LoopStatement)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                    })
                }
                Instruction::IfQueueAllowsContinue(_) => {
                    Instruction::IfQueueAllowsContinue({
                        let mut g_ids: Vec<usize> = vec![];
                        db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'content'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting content inside IfQueueAllowsContinue)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                        let mut result = vec![];
                        for g_id in &g_ids {
                            result
                                .push(Self::instruction(db_connection, *g_id));
                        }
                        result
                    })
                }
            }
        })
    }
    #[allow(clippy::too_many_lines)]
    fn instruction_value(
        db_connection: &mut PooledConn,
        instruction_id: Option<usize>,
        instruction_value_id: Option<usize>,
        branch: Option<String>,
    ) -> Option<InstructionValue> {
        let mut result: Option<InstructionValue> = None;
        let mut iv_id = 0;

        db_connection.query_map(
            if let Some(instruction_id) = instruction_id {
                format!(
                    "
                        SELECT
                            iv.id,
                            iv.variant,
                            iv.int_val,
                            iv.str_val,
                            iv.bool_val
                        FROM instruction_values iv
                        JOIN instructions i ON i.id = iv.instruction_id
                        WHERE iv.branch = '{}' AND i.id = {}
                    ",
                    branch.unwrap_or_default(),
                    instruction_id
                )
            } else if let Some(instruction_value_id) = instruction_value_id {
                format!(
                    "
                        SELECT
                            ivv.id,
                            ivv.variant,
                            ivv.int_val,
                            ivv.str_val,
                            ivv.bool_val
                        FROM instruction_values iv
                            JOIN instruction_values ivv ON ivv.instruction_value_id = iv.id
                            WHERE ivv.branch = '{}' AND ivv.instruction_value_id = {}
                    ",
                    branch.unwrap_or_default(),
                    instruction_value_id
                )
            } else {
                return None
            },
            |(id, variant, int_val, str_val, _bool_val): (usize, String, usize, String, bool)| {
                iv_id = id;
                result = match variant.as_str() {
                    "Number" => {
                        #[allow(clippy::cast_possible_truncation, clippy::as_conversions)]
                        Some(
                        InstructionValue::Number (
                            int_val
                        ))
                    }
                    "BlueprintSpeedOfFixture" => Some(
                        InstructionValue::BlueprintSpeedOfFixture(int_val)
                    ),
                    "BlueprintIntensityOfFixture" => Some(
                        InstructionValue::BlueprintIntensityOfFixture(int_val)
                    ),
                    "Variable" => Some(InstructionValue::Variable(str_val)),
                    "Keypoint" => Some(InstructionValue::Keypoint(int_val)),
                    "Function" => Some(InstructionValue::Function(FunctionGetter {
                        fixture_id: int_val,
                        function: str_val,
                    })),
                    "Value" => Some(InstructionValue::Value),
                    "Pressed" => Some(InstructionValue::Pressed),
                    "Released" => Some(InstructionValue::Released),
                    "Default" => Some(InstructionValue::Default),
                    "Bpm" => Some(InstructionValue::Bpm),
                    "BpmModifier" => Some(InstructionValue::BpmModifier),
                    "ProgramStartTimestamp" => Some(InstructionValue::ProgramStartTimestamp),
                    "MathOperator" => Some(InstructionValue::MathOperator(Box::new(MathOperator {
                        left_value: None,
                        right_value: None,
                        operand: str_val.into(),
                    }))),
                    "Random" => Some(InstructionValue::Random((Box::new(None), Box::new(None)))),
                    val => {
                        logging::log(format!("Unknown instruction_value: {val}"), logging::LogLevel::Warning, true);
                        None
                    }
                };
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!(
                    "{err:?}\n        (Selecting instructionvalue)"
                ),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
        if let Some(InstructionValue::MathOperator(ref mut math_operator)) =
            result
        {
            math_operator.left_value = Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("left_value".to_owned()),
            );
            math_operator.right_value = Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("right_value".to_owned()),
            );
        }
        if let Some(InstructionValue::Random(ref mut random)) = result {
            random.0 = Box::new(Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("left_value".to_owned()),
            ));
            random.1 = Box::new(Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("right_value".to_owned()),
            ));
        }
        result
    }

    pub fn create_default_snippet_directory(
        &mut self,
        parent_dir_id: usize,
    ) -> Option<usize> {
        self.db_connection()
            .query_drop(format!(
                "
                    INSERT INTO snippet_dirs (parent_dir_id, name)
                    VALUES ({parent_dir_id}, 'NEW')
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new snippet_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let created_id = self.db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippet_dirs;
                "
                ,
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Selecting just created snippets_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }
    pub fn create_root_snippet_directory(&mut self) -> Option<usize> {
        self.db_connection()
            .query_drop(
                "
                    INSERT INTO snippet_dirs (name, show_id)
                    VALUES ('', (SELECT id FROM shows WHERE active))
                ",
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting root snippet_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let created_id = self.db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippet_dirs;
                "
                ,
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Selecting just created snippets_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }
    pub fn create_default_snippet(
        &mut self,
        parent_dir_id: usize,
    ) -> Option<usize> {
        self.db_connection()
            .query_drop(format!(
                "
                    INSERT INTO snippets (name, parent_dir_id, do_not_use_instructions)
                    VALUES ('NEW', {parent_dir_id}, 0)
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new snippet)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let created_id = self
            .db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippets;
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Selecting just created snippet)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }

    pub fn create_snippet_with_data(
        &mut self,
        snippet: Snippet,
        parent_dir_id: usize,
    ) -> Option<usize> {
        self.db_connection()
            .query_drop(format!(
                "
                    INSERT INTO snippets (name, parent_dir_id, do_not_use_instructions, category, serial_module_key)
                    VALUES ('{}', {}, {}, '{}', {})
                ",
                snippet.name,
                parent_dir_id,
                u8::from(snippet.do_not_use_instructions),
                snippet.category,
                snippet.serial_module_key.map_or("NULL".to_string(), |k| k.to_string())
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new snippet with data)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        let created_id = self
            .db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippets;
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Selecting just created snippet with data)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();

        if let Some(snippet_id) = created_id {
            self.set_snippet_instructions(snippet_id, &snippet.instructions);

            if let Some(reason) = snippet.requires_user_action_reason {
                self.set_snippet_requires_user_action_reason(
                    snippet_id,
                    &Some(reason),
                );
            }
        }

        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }

    pub fn delete_snippets_dir(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM snippet_dirs
                    WHERE id = {id}
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting snippets_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn delete_snippet(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM snippets
                    WHERE id = {id}
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting snippet)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippets_dir_name(
        &mut self,
        directory_id: usize,
        new_name: &String,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippet_dirs
                    SET name = '{new_name}'
                    WHERE id = {directory_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet_dir name)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_name(&mut self, snippet_id: usize, new_name: &String) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET name = '{new_name}'
                    WHERE id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet name)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_category(
        &mut self,
        snippet_id: usize,
        new_category: SnippetCategory,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET category = '{new_category}'
                    WHERE id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet category)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_do_not_use_instructions(
        &mut self,
        snippet_id: usize,
        new_do_not_use_instructions: bool,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET do_not_use_instructions = '{}'
                    WHERE id = {snippet_id};
                ", u8::from(new_do_not_use_instructions)
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet new_do_not_use_instructions)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_serial_module_key(
        &mut self,
        snippet_id: usize,
        new_serial_module_key: Option<u16>,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET serial_module_key = {}
                    WHERE id = {snippet_id};
                ",
                new_serial_module_key.map_or("NULL".to_string(), |key| key.to_string())
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet new_serial_module_key)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_requires_user_action_reason(
        &mut self,
        snippet_id: usize,
        new_requires_user_action_reason: &Option<String>,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET requires_user_action_reason = '{}'
                    WHERE id = {snippet_id};
                ",
                new_requires_user_action_reason.clone().unwrap_or_default()
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet new_requires_user_action_reason)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_instructions(
        &mut self,
        snippet_id: usize,
        content: &[Instruction],
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE i FROM instructions i
                    LEFT JOIN snippets s ON s.id = i.snippet_id
                    WHERE s.id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Removing old instructions)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        for instruction in content {
            self.create_instruction(Some(snippet_id), None, instruction, None);
        }
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn move_snippet_to_directory(
        &mut self,
        snippet_id: usize,
        new_parent_dir_id: usize,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET parent_dir_id = {new_parent_dir_id}
                    WHERE id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Moving snippet to directory)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn move_snippet_directory_to_directory(
        &mut self,
        directory_id: usize,
        new_parent_dir_id: usize,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippet_dirs
                    SET parent_dir_id = {new_parent_dir_id}
                    WHERE id = {directory_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Moving snippet directory to directory)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    #[allow(clippy::too_many_lines, clippy::cognitive_complexity)]
    fn create_instruction(
        &mut self,
        snippet_id: Option<usize>,
        parent_instruction_id: Option<usize>,
        instruction: &Instruction,
        branch: Option<String>,
    ) {
        match instruction {
            Instruction::SetBlueprintIntensity(intensity_of_blueprints) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "SetBlueprintIntensity",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(intensity_of_blueprints) = intensity_of_blueprints {
                    self.persist_instruction_value(
                        intensity_of_blueprints,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::SetSpeedOfBlueprints(speed_of_blueprints) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "SetSpeedOfBlueprints",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(speed_of_blueprints) = speed_of_blueprints {
                    self.persist_instruction_value(
                        speed_of_blueprints,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::PanTo(pt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "PanTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(pt) = pt {
                    self.persist_instruction_value(
                        pt,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::TiltTo(tt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "TiltTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(tt) = tt {
                    self.persist_instruction_value(
                        tt,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::ColorTo(ct) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "ColorTo",
                    None,
                    Some(&ct.color.as_hex()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(fade_duration_beat_count) = &ct.fade_duration_beat_count {
                    self.persist_instruction_value(
                        fade_duration_beat_count,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::BpmTo(bt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "BpmTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(bt) = bt {
                    self.persist_instruction_value(
                        bt,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::ColorToRandom(fade_duration) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "ColorToRandom",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(fade_duration) = fade_duration {
                    self.persist_instruction_value(
                        fade_duration,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::ActivateAllFixtures => {
                self.persist_instruction(
                    branch,
                    "ActivateAllFixtures",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::StartRecording => {
                self.persist_instruction(
                    branch,
                    "StartRecording",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::StopRecording => {
                self.persist_instruction(
                    branch,
                    "StopRecording",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ClearRecording => {
                self.persist_instruction(
                    branch,
                    "ClearRecording",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ExecuteCallableSnippet(excs) => {
                self.persist_instruction(
                    branch,
                    "ExecuteCallableSnippet",
                    Some(*excs),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ActivateFixtureById(afn) => {
                self.persist_instruction(
                    branch,
                    "ActivateFixtureById",
                    Some(*afn),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ActivateFixtureGroup(afg) => {
                self.persist_instruction(
                    branch,
                    "ActivateFixtureGroup",
                    None,
                    Some(afg),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::BlueprintTo(bpt) => {
                #[allow(clippy::cast_possible_truncation)]
                self.persist_instruction(
                    branch,
                    "BlueprintTo",
                    Some(bpt.id),
                    None,
                    Some(bpt.oneshot),
                    Some(bpt.delay),
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ClearBlueprint(blueprint_id) => {
                self.persist_instruction(
                    branch,
                    "ClearBlueprint",
                    Some(*blueprint_id),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::TimecodeTo(tct) => {
                #[allow(clippy::cast_possible_truncation)]
                self.persist_instruction(
                    branch,
                    "TimecodeTo",
                    Some(tct.id),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::SetBlueprintPositionIndexOffsetMode(sbpiom) => {
                self.persist_instruction(
                    branch,
                    "SetBlueprintPositionIndexOffsetMode",
                    None,
                    Some(&sbpiom.to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ToggleQueueMode(queue_mode) => {
                self.persist_instruction(
                    branch,
                    "ToggleQueueMode",
                    None,
                    Some(&(*queue_mode).to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::AddToPan(atp) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "AddToPan",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );

                if let Some(atp) = atp {
                    self.persist_instruction_value(
                        atp,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::AddToTilt(att) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "AddToTilt",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(att) = att {
                    self.persist_instruction_value(
                        att,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::PositionTo(pos) => {
                #[allow(clippy::cast_possible_truncation)]
                self.persist_instruction(
                    branch,
                    "PositionTo",
                    Some(*pos),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::BpmModifierTo(bmt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "BpmModifierTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(bmt) = bmt {
                    self.persist_instruction_value(
                        bmt,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::FixtureLoop(fx_loop) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "FixtureLoop",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in &fx_loop.fixtures {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("fixtures".to_owned()),
                    );
                }
                for instruction in &fx_loop.instructions {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("instructions".to_owned()),
                    );
                }
            }
            Instruction::CreateVariable(cv) => {
                self.persist_instruction(
                    branch,
                    "CreateVariable",
                    None,
                    Some(cv),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::SetVariable(sv) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "SetVariable",
                    None,
                    Some(&sv.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(ref value) = sv.value {
                    self.persist_instruction_value(
                        value,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::AddToGroup(atg) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "AddToGroup",
                    None,
                    Some(&atg.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in &atg.fixtures {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("fixtures".to_owned()),
                    );
                }
            }
            Instruction::RemoveFromGroup(rfg) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "RemoveFromGroup",
                    None,
                    Some(&rfg.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in &rfg.fixtures {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("fixtures".to_owned()),
                    );
                }
            }
            Instruction::DelayByNotes(delay_instruction) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "DelayByNotes",
                    None,
                    Some(&delay_instruction.note_type.into()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(count) = &delay_instruction.count {
                    self.persist_instruction_value(
                        count,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::DeselectAllFixtures => {
                self.persist_instruction(
                    branch,
                    "DeselectAllFixtures",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::DimmerTo(dt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "DimmerTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );

                if let Some(dt) = dt {
                    self.persist_instruction_value(
                        dt,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::UnimplementedChannelTo(uct) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "UnimplementedChannelTo",
                    None,
                    Some(&uct.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(ref value) = uct.value {
                    self.persist_instruction_value(
                        value,
                        Some(inst_id),
                        None,
                        Some("value"),
                    );
                }
                if let Some(ref fade_duration) = uct.fade_duration_beat_count {
                    self.persist_instruction_value(
                        fade_duration,
                        Some(inst_id),
                        None,
                        Some("fade_duration"),
                    );
                }
            }
            Instruction::IfStatement(if_statement) => {
                let if_statement_id = self.persist_instruction(
                    branch,
                    "IfStatement",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for (index, conditional_block) in
                    if_statement.iter().enumerate()
                {
                    let conditional_block_id = self.persist_instruction(
                        None,
                        "ConditionalBlock",
                        Some(index),
                        Some(
                            &conditional_block
                                .comparison
                                .comparator
                                .to_string(),
                        ),
                        None,
                        None,
                        None,
                        Some(if_statement_id),
                    );

                    if let Some(ref value) =
                        conditional_block.comparison.left_value
                    {
                        self.persist_instruction_value(
                            value,
                            Some(conditional_block_id),
                            None,
                            Some("left_value"),
                        );
                    }
                    if let Some(ref value) =
                        conditional_block.comparison.right_value
                    {
                        self.persist_instruction_value(
                            value,
                            Some(conditional_block_id),
                            None,
                            Some("right_value"),
                        );
                    }

                    for instruction in &conditional_block.content {
                        self.create_instruction(
                            None,
                            Some(conditional_block_id),
                            instruction,
                            Some("content".to_owned()),
                        );
                    }
                }
            }
            Instruction::LoopStatement(ls) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "LoopStatement",
                    None,
                    Some(&ls.comparison.comparator.to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(ref value) = ls.comparison.left_value {
                    self.persist_instruction_value(
                        value,
                        Some(inst_id),
                        None,
                        Some("left_value"),
                    );
                }
                if let Some(ref value) = ls.comparison.right_value {
                    self.persist_instruction_value(
                        value,
                        Some(inst_id),
                        None,
                        Some("right_value"),
                    );
                }
                for instruction in &ls.content {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("content".to_owned()),
                    );
                }
            }
            Instruction::IfQueueAllowsContinue(iqac) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "IfQueueAllowsContinue",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in iqac {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("content".to_owned()),
                    );
                }
            }
            Instruction::Print(print_statement) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "Print",
                    None,
                    Some(&print_statement.text),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(value) = &print_statement.value {
                    self.persist_instruction_value(
                        value,
                        Some(inst_id),
                        None,
                        Some("value"),
                    );
                }
            }
            Instruction::Nop => {
                self.persist_instruction(
                    branch,
                    "Nop",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
        }
    }
    #[allow(clippy::too_many_arguments)]
    fn persist_instruction(
        &self,
        branch: Option<String>,
        variant: &str,
        int_val: Option<usize>,
        str_val: Option<&String>,
        bool_val_1: Option<bool>,
        bool_val_2: Option<bool>,
        snippet_id: Option<usize>,
        parent_instruction_id: Option<usize>,
    ) -> usize {
        self.db_connection()
            .query_drop(format!(
                "
                        INSERT INTO instructions
                        (
                            branch,
                            variant,
                            int_val,
                            str_val,
                            bool_val_1,
                            bool_val_2,
                            {}
                        )
                        VALUES
                        (
                            '{}',
                            '{variant}',
                            {},
                            '{}',
                            {},
                            {},
                            {}
                        )
                    ",
                if snippet_id.is_some() {
                    "snippet_id"
                } else {
                    "parent_instruction_id"
                },
                branch.unwrap_or_default(),
                int_val.unwrap_or_default(),
                str_val.unwrap_or(&String::new()),
                bool_val_1.unwrap_or_default(),
                bool_val_2.unwrap_or_default(),
                snippet_id.unwrap_or_else(
                    || parent_instruction_id.unwrap_or_default()
                )
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Creating new instruction)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let mut instruction_id = 0;
        self.db_connection()
                    .query_map(
                        "
                    SELECT MAX(id) FROM instructions

                    ",
                        |id| instruction_id = id,
                    )
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                        "{err:?}\n        (Selecting the just-created instruction)"
                    ),
                            logging::LogLevel::DbError,
                            true,
                        );
                        vec![]
                    });
        instruction_id
    }
    #[allow(clippy::too_many_arguments)]
    fn create_instruction_value(
        &self,
        variant: &str,
        branch: Option<&str>,
        int_val: Option<usize>,
        str_val: Option<&String>,
        bool_val: Option<bool>,
        instruction_id: Option<usize>,
        instruction_value_id: Option<usize>,
    ) -> usize {
        self.db_connection()
            .query_drop(format!(
                "
                INSERT INTO instruction_values
                (
                    variant,
                    branch,
                    int_val,
                    str_val,
                    bool_val,
                    {}
                )
                VALUES
                (
                    '{variant}',
                    '{}',
                    {},
                    '{}',
                    {},
                    {}
                )
            ",
                if instruction_id.is_some() {
                    "instruction_id"
                } else {
                    "instruction_value_id"
                },
                branch.unwrap_or_default(),
                int_val.unwrap_or_default(),
                str_val.unwrap_or(&String::new()),
                bool_val.unwrap_or(false),
                instruction_id
                    .unwrap_or_else(|| instruction_value_id.unwrap_or_default())
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Creating new instruction_value)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let mut instruction_value_id = 0;
        self.db_connection()
                    .query_map(
                        "
                    SELECT MAX(id) FROM instruction_values

                    ",
                        |id| instruction_value_id = id,
                    )
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                        "{err:?}\n        (Selecting the just-created instruction_value)"
                    ),
                            logging::LogLevel::DbError,
                            true,
                        );
                        vec![]
                    });
        instruction_value_id
    }
    #[allow(clippy::too_many_lines)]
    fn persist_instruction_value(
        &mut self,
        instruction_value: &InstructionValue,
        instruction_id: Option<usize>,
        instruction_value_id: Option<usize>,
        branch: Option<&str>,
    ) {
        match instruction_value {
            InstructionValue::Number(value) => {
                self.create_instruction_value(
                    "Number",
                    branch,
                    Some(*value),
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Variable(str) => {
                self.create_instruction_value(
                    "Variable",
                    branch,
                    None,
                    Some(str),
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Function(fn_get) => {
                self.create_instruction_value(
                    "Function",
                    branch,
                    Some(fn_get.fixture_id),
                    Some(&fn_get.function),
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Value => {
                self.create_instruction_value(
                    "Value",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Pressed => {
                self.create_instruction_value(
                    "Pressed",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Released => {
                self.create_instruction_value(
                    "Released",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Default => {
                self.create_instruction_value(
                    "Default",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Bpm => {
                self.create_instruction_value(
                    "Bpm",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Keypoint(kp_id) => {
                self.create_instruction_value(
                    "Keypoint",
                    branch,
                    Some(*kp_id),
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::BpmModifier => {
                self.create_instruction_value(
                    "BpmModifier",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::ProgramStartTimestamp => {
                self.create_instruction_value(
                    "ProgramStartTimestamp",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::BlueprintSpeedOfFixture(bfs) => {
                self.create_instruction_value(
                    "BlueprintSpeedOfFixture",
                    branch,
                    Some(*bfs),
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::BlueprintIntensityOfFixture(bif) => {
                self.create_instruction_value(
                    "BlueprintIntensityOfFixture",
                    branch,
                    Some(*bif),
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::MathOperator(mo) => {
                let instruction_value_id: usize = self
                    .create_instruction_value(
                        "MathOperator",
                        branch,
                        None,
                        Some(&format!("{:?}", mo.operand)),
                        None,
                        instruction_id,
                        instruction_value_id,
                    );
                if let Some(ref left_value) = mo.left_value {
                    self.persist_instruction_value(
                        left_value,
                        None,
                        Some(instruction_value_id),
                        Some("left_value"),
                    );
                }

                if let Some(ref right_value) = mo.right_value {
                    self.persist_instruction_value(
                        right_value,
                        None,
                        Some(instruction_value_id),
                        Some("right_value"),
                    );
                }
            }
            InstructionValue::Random(rand) => {
                let instruction_value_id: usize = self
                    .create_instruction_value(
                        "Random",
                        branch,
                        None,
                        None,
                        None,
                        instruction_id,
                        instruction_value_id,
                    );
                if let Some(ref zero) = *rand.0.clone() {
                    self.persist_instruction_value(
                        zero,
                        None,
                        Some(instruction_value_id),
                        Some("left_value"),
                    );
                }
                if let Some(ref one) = *rand.1.clone() {
                    self.persist_instruction_value(
                        one,
                        None,
                        Some(instruction_value_id),
                        Some("right_value"),
                    );
                }
            }
        }
    }
}
