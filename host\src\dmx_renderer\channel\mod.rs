use serde::{Deserialize, Serialize};
use splines::Spline;
use ts_rs::TS;

pub mod color_channels;
pub mod movement_channels;
pub mod unimplemented_channels;

#[derive(
    Debug, <PERSON>ialEq, Eq, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize, TS, Default,
)]
#[ts(export)]
pub struct DmxChannelValue {
    pub channel: usize,
    #[serde(skip)]
    pub value: u8,
}
impl From<(usize, u8)> for DmxChannelValue {
    fn from(value: (usize, u8)) -> Self {
        Self {
            channel: value.0,
            value: value.1,
        }
    }
}

pub trait DmxChannelEmitter {
    fn compute_dmx_channels(
        &mut self,
        master_dimmer: f32,
    ) -> Vec<DmxChannelValue>;
}

#[must_use]
#[allow(clippy::modulo_arithmetic)]
pub fn map_index_to_spline_length(
    value: f32,
    spline: &Spline<f32, f32>,
) -> f32 {
    if value < 0. {
        return 0.;
    }
    let max_key = spline.keys().last().map_or(0., |key| key.t);
    if max_key > 0. {
        value % (max_key - 1.)
    } else {
        0.
    }
}

#[must_use]
#[allow(clippy::suboptimal_flops)]
pub fn apply_intensity_to_spline_sample(
    sample: f32,
    blueprint_intensity: f32,
) -> f32 {
    #[allow(clippy::as_conversions, clippy::cast_precision_loss)]
    let reduced_range = 255.0 * blueprint_intensity;
    let reduced_min = (255.0 - reduced_range) / 2.0;
    let reduced_max = reduced_min + reduced_range;
    (sample / 255.0) * (reduced_max - reduced_min) + reduced_min
}
