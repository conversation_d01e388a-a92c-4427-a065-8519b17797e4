use super::{map_index_to_spline_length, DmxChannelEmitter, DmxChannelValue};
use crate::dmx_renderer::dynamics::IsDynamic;
use crate::dmx_renderer::{
    channel::apply_intensity_to_spline_sample,
    dynamics::property::PropertyFileDescriptor,
};
use crate::input_parser::structs::InstructionValue;
use serde::{Deserialize, Serialize};
use splines::{Key, Spline};
use ts_rs::TS;

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct Keypoint {
    pub id: Option<usize>,
    pub name: String,
    pub value: u8,
}

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct UnimplementedChannel {
    pub id: Option<usize>,
    pub channel: usize,
    pub name: String,
    pub dimmable: bool,
    pub inverted: bool,
    pub default: u8,
    pub keypoints: Vec<Keypoint>,
    #[serde(skip)]
    pub value: u8,
    #[serde(skip)]
    pub spline: Option<Spline<f32, f32>>,
    #[serde(skip)]
    pub oneshot: bool,
    #[serde(skip)]
    pub blueprint_id: Option<usize>,
}

impl UnimplementedChannel {
    #[must_use]
    pub fn name(&self) -> String {
        self.name.clone()
    }
    #[must_use]
    pub const fn value(&self) -> u8 {
        self.value
    }
    pub fn set_to_default(
        &mut self,
        fade_duration_beat_count: usize,
        current_index: f32,
    ) {
        self.clear_all_splines();

        self.oneshot = true;
        self.spline = Some(Spline::from_vec(vec![
            Key::new(
                current_index,
                self.value.into(),
                splines::Interpolation::Linear,
            ),
            #[allow(
                clippy::suboptimal_flops,
                clippy::cast_precision_loss,
                clippy::cast_possible_truncation,
                clippy::cast_sign_loss,
                clippy::as_conversions
            )]
            Key::new(
                current_index
                    + (fade_duration_beat_count.saturating_mul(1_000)) as f32,
                self.default.into(),
                splines::Interpolation::Linear,
            ),
        ]));
    }
    pub fn set_value(
        &mut self,
        value: usize,
        fade_duration_beat_count: usize,
        current_index: f32,
    ) {
        self.clear_all_splines();

        self.oneshot = true;
        self.spline = Some(Spline::from_vec(vec![
            Key::new(
                current_index,
                self.value.into(),
                splines::Interpolation::Linear,
            ),
            #[allow(
                clippy::suboptimal_flops,
                clippy::cast_precision_loss,
                clippy::cast_possible_truncation,
                clippy::cast_sign_loss,
                clippy::as_conversions
            )]
            Key::new(
                current_index
                    + (fade_duration_beat_count.saturating_mul(1_000)) as f32,
                value.clamp(0, f32::MAX as usize) as f32,
                splines::Interpolation::Linear,
            ),
        ]));
    }
    #[must_use]
    pub fn keypoints(&self) -> Vec<&Keypoint> {
        self.keypoints.iter().collect()
    }
    #[must_use]
    pub fn from_unimplemented_channel_file_descriptors(
        values: Vec<&mut Self>,
    ) -> Vec<Self> {
        let mut result = vec![];
        for value in values {
            value.value = value.default;
            result.push(value.clone());
        }
        result
    }
}

impl IsDynamic for UnimplementedChannel {
    fn extract_internal_spline_from(
        &mut self,
        properties: &[PropertyFileDescriptor],
        oneshot: bool,
        blueprint_id: Option<usize>,
    ) {
        self.oneshot = oneshot;
        for property in properties {
            if let PropertyFileDescriptor::UnimplementedChannel((name, _, _)) =
                property
            {
                if *name == self.name() {
                    self.spline = Some(property.into());
                    self.blueprint_id = blueprint_id;
                }
            }
        }
    }
    fn clear_all_splines(&mut self) {
        self.spline = None;
        self.blueprint_id = None;
    }
    fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize) {
        if self.blueprint_id == Some(blueprint_id) {
            self.clear_all_splines();
        }
    }
    fn apply_spline_index(&mut self, index: f32, blueprint_intensity: f32) {
        if let Some(spline) = self.spline.as_mut() {
            if self.oneshot {
                if let Some(last) = spline.keys().last() {
                    #[allow(
                        clippy::cast_precision_loss,
                        clippy::cast_possible_truncation,
                        clippy::cast_sign_loss,
                        clippy::as_conversions
                    )]
                    if index >= last.t {
                        let mut value = last.value;

                        value = apply_intensity_to_spline_sample(
                            value,
                            blueprint_intensity,
                        );

                        self.value = value.clamp(0., u8::MAX.into()) as u8;
                        self.clear_all_splines();
                        return;
                    }
                }
            }
            #[allow(
                clippy::cast_possible_truncation,
                clippy::cast_sign_loss,
                clippy::as_conversions
            )]
            if let Some(sample) =
                spline.clamped_sample(map_index_to_spline_length(index, spline))
            {
                let final_value = apply_intensity_to_spline_sample(
                    sample,
                    blueprint_intensity,
                );

                self.value = final_value.clamp(0., u8::MAX.into()) as u8;
            }
        }
    }
}

impl DmxChannelEmitter for UnimplementedChannel {
    #[allow(clippy::useless_let_if_seq)]
    fn compute_dmx_channels(
        &mut self,
        master_dimmer: f32,
    ) -> Vec<DmxChannelValue> {
        let mapped_value = f32::from(self.value);
        let mut value = self.value;
        #[allow(
            clippy::cast_possible_truncation,
            clippy::cast_sign_loss,
            clippy::as_conversions
        )]
        if self.dimmable {
            value =
                (mapped_value * master_dimmer).clamp(0., u8::MAX.into()) as u8;
        }

        if self.inverted {
            value = 255_u8.saturating_sub(value);
        }

        vec![(self.channel, value).into()]
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct UnimplementedChannelSetter {
    pub value: Option<InstructionValue>,
    pub name: String,
    pub fade_duration_beat_count: Option<InstructionValue>,
}

impl PartialEq for UnimplementedChannel {
    fn eq(&self, other: &Self) -> bool {
        self.channel == other.channel
            && self.name == other.name
            && self.dimmable == other.dimmable
            && self.inverted == other.inverted
            && self.default == other.default
            && self.value == other.value
            && self.oneshot == other.oneshot
            && self.blueprint_id == other.blueprint_id
    }
}
