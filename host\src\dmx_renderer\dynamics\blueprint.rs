use crate::database_handler::Db<PERSON><PERSON><PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::dynamics::property::{
    ColorPropertyCoordinate, PanTiltPositionPropertyCoordinate,
    PropertyFileDescriptor, UnimplementedChannelPropertyCoordinate,
};
use crate::dmx_renderer::fixture::DmxFixture;
use crate::input_parser::beatduration::{BeatDuration, NoteType};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

pub const DEFAULT_NAME: &str = "new";
pub const DEFAULT_REQUIRES_USER_ACTION_REASON: Option<&str> = Some("empty");

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct BlueprintFileDescriptor {
    pub properties: Vec<PropertyFileDescriptor>,
    pub registered_fixture_delays: Vec<RegisteredBlueprintFixtureDelay>,
    pub name: String,
    pub id: usize,
    pub requires_user_action_reason: Option<String>,
}

impl BlueprintFileDescriptor {
    #[must_use]
    pub fn max_len_in_beats(
        // TODO: could this be just & instead of &mut?
        db_handler: &mut DbHandler,
        id: usize,
    ) -> BeatDuration {
        #[allow(
            clippy::cast_possible_truncation,
            clippy::cast_sign_loss,
            clippy::as_conversions
        )]
        let mut bp_duration_in_sixteenth: Vec<usize> =
            DbHandler::blueprint(&mut db_handler.db_connection(), id)
                .properties
                .iter()
                .map(|property| match property {
                    PropertyFileDescriptor::UnimplementedChannel(channel) => {
                        channel
                            .1
                            .last()
                            // TODO: use `unwrap_or_default`
                            .unwrap_or(
                                &UnimplementedChannelPropertyCoordinate {
                                    x: 0.,
                                    y: 0.,
                                },
                            )
                            .x
                            .trunc() as usize
                            / 1_000
                    }
                    PropertyFileDescriptor::PanTiltPositions(
                        pantiltpositions,
                        _,
                    ) => {
                        pantiltpositions
                            .last()
                            // TODO: use `unwrap_or_default`
                            .unwrap_or(&PanTiltPositionPropertyCoordinate {
                                position_id: 0,
                                position_name: String::new(),
                                x: 0.,
                            })
                            .x
                            .trunc() as usize
                            / 1_000
                    }
                    PropertyFileDescriptor::ColorPropertyCoordinates(
                        color,
                        _,
                    ) => {
                        color
                            .last()
                            // TODO: use `unwrap_or_default`
                            .unwrap_or(&ColorPropertyCoordinate {
                                x: 0.,
                                color: RgbColor {
                                    red: 0,
                                    green: 0,
                                    blue: 0,
                                },
                            })
                            .x
                            .trunc() as usize
                            / 1_000
                    }
                    PropertyFileDescriptor::CallSnippet(snippetcall) => {
                        DmxFixture::spline_index_from_nanos(snippetcall.x)
                            .trunc() as usize
                    }
                })
                .collect();
        bp_duration_in_sixteenth.sort_unstable();
        let Some(bp_duration_in_sixteenth) = bp_duration_in_sixteenth.last()
        else {
            return BeatDuration::default();
        };
        BeatDuration::new(NoteType::Quarter, *bp_duration_in_sixteenth)
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct RegisteredBlueprintFixtureDelay {
    pub fixture_id: usize,
    pub delay_eights: usize,
}
