use property::PropertyFileDescriptor;

pub mod blueprint;
pub mod property;
pub mod timecode;

/// With the current approach to dynamics, all channels must clear theyr spline in the
/// value setter!
pub trait IsDynamic
where
    Self: core::fmt::Debug,
{
    fn apply_spline_index(&mut self, index: f32, blueprint_intensity: f32);
    fn extract_internal_spline_from(
        &mut self,
        properties: &[PropertyFileDescriptor],
        oneshot: bool,
        blueprint_id: Option<usize>,
    );
    fn clear_all_splines(&mut self);
    fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize);
}
