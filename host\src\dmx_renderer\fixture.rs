use super::channel::{
    movement_channels::MovementChannels,
    unimplemented_channels::UnimplementedChannel, DmxChannelEmitter,
};
use super::dynamics::property::SnippetCall;
use super::dynamics::timecode::{
    all_properties_from_tracks, merge_by_type_for_fixture,
    TimecodeFileDescriptor, TimedPropertyFileDescriptor,
};
use rand::random;
use serde::{Deserialize, Serialize};
use ts_rs::TS;

use super::dynamics::blueprint::BlueprintFileDescriptor;
use super::dynamics::IsDynamic;
use crate::database_handler::pan_tilt_positions::{
    ComposedPanTiltPosition, FixturePosition,
};
use crate::database_handler::<PERSON>b<PERSON><PERSON><PERSON>;
use crate::dmx_renderer::channel::color_channels::{DmxFixtureColor, RgbColor};
use crate::input_parser::structs::InputParser;
use crate::logging::{self};

use core::fmt;
use core::ops::RangeInclusive;
use core::time::Duration;

pub const BLUEPRINT_SPEED_RANGE: RangeInclusive<f32> = 0.0..=1.;
pub const BLUEPRINT_INTENSITY_RANGE: RangeInclusive<f32> = 0.0..=1.;
const DIMMER_RANGE: RangeInclusive<f32> = 0.0..=1.0;

#[derive(Clone, Copy, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum PositionIndexOffsetMode {
    Matching,
    CustomOffset,
    Random,
    StageLeftToRight,
    StageRightToLeft,
    StageUpToDown,
    StageDownToUp,
}
impl From<String> for PositionIndexOffsetMode {
    fn from(value: String) -> Self {
        match value.as_str() {
            "CustomOffset" => Self::CustomOffset,
            "Random" => Self::Random,
            "StageLeftToRight" => Self::StageLeftToRight,
            "StageRightToLeft" => Self::StageRightToLeft,
            "StageUpToDown" => Self::StageUpToDown,
            "StageDownToUp" => Self::StageDownToUp,
            _ /* | "Matching" */ => Self::Matching,
        }
    }
}
impl fmt::Display for PositionIndexOffsetMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Matching => write!(f, "Matching"),
            Self::CustomOffset => write!(f, "CustomOffset"),
            Self::Random => write!(f, "Random"),
            Self::StageRightToLeft => {
                write!(f, "StageRightToLeft")
            }
            Self::StageLeftToRight => {
                write!(f, "StageLeftToRight")
            }
            Self::StageUpToDown => {
                write!(f, "StageUpToDown")
            }
            Self::StageDownToUp => {
                write!(f, "StageDownToUp")
            }
        }
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct DmxFixtureFileDescriptor {
    pub id: Option<usize>,
    pub dmx_address: Option<usize>,
    pub dmx_universe: u16,
    pub name: String,
    pub fixturetype: String,
    pub movement_channels: Option<MovementChannels>,
    pub stage_coordinates: (usize, usize),
    pub footprint_size: usize,
    pub unimplemented_channels: Vec<UnimplementedChannel>,
    pub color: Option<DmxFixtureColor>,
}
pub struct DmxFixture {
    id: usize,
    name: String,
    index: f32,
    dmx_address: Option<usize>,
    dmx_universe: u16,
    fixturetype: String,
    position_index_offset_mode: PositionIndexOffsetMode,
    enabled: bool,
    color: Option<DmxFixtureColor>,
    stage_coordinates: (usize, usize),
    footprint_size: usize,
    movement_channels: Option<MovementChannels>,
    unimplemented_channels: Vec<UnimplementedChannel>,
    blueprint_delay_eights: f32,
    blueprint_random_delay: f32,
    dimmer_value: f32,
    snippet_calls_of_current_timecode: Vec<SnippetCall>,
    speed_of_blueprints: f32,
    blueprint_intensity: f32,
}

impl DmxFixture {
    #[must_use]
    pub fn new(raw_fixture: DmxFixtureFileDescriptor) -> Self {
        let id = raw_fixture.id.unwrap_or_else(|| {
            logging::log(
                format!(
                    "The show file might be corrupted! Trying to load fixture `{:?}` without id",
                    raw_fixture.name
                ),
                logging::LogLevel::Warning,
                true,
            );
            0
        });
        let mut mutable_raw_fixture = raw_fixture.clone();
        Self {
            id,
            name: raw_fixture.name,
            index: 0.,
            dmx_address: raw_fixture.dmx_address,
            dmx_universe: raw_fixture.dmx_universe,
            fixturetype: raw_fixture.fixturetype,
            position_index_offset_mode: PositionIndexOffsetMode::Matching,
            enabled: true,
            color: raw_fixture.color,
            stage_coordinates: raw_fixture.stage_coordinates,
            footprint_size: raw_fixture.footprint_size,
            movement_channels: raw_fixture.movement_channels,
            unimplemented_channels:
                UnimplementedChannel::from_unimplemented_channel_file_descriptors(
                    mutable_raw_fixture.unimplemented_channels.iter_mut().collect()
            ),
            blueprint_delay_eights: 0.,
            blueprint_random_delay: 0.,
            dimmer_value: *DIMMER_RANGE.end(),
            snippet_calls_of_current_timecode: vec![],
            speed_of_blueprints: *BLUEPRINT_SPEED_RANGE.end(),
            blueprint_intensity: *BLUEPRINT_INTENSITY_RANGE.end(),
        }
    }
    #[must_use]
    pub const fn id(&self) -> usize {
        self.id
    }
    #[must_use]
    pub const fn movement_channels(&self) -> &Option<MovementChannels> {
        &self.movement_channels
    }
    pub fn merge_file_descriptor_with_state(
        &mut self,
        file_descriptor: &DmxFixtureFileDescriptor,
    ) {
        self.name.clone_from(&file_descriptor.name);
        if let Some(colored) = &file_descriptor.color {
            if let Some(existing_color) = &mut self.color {
                existing_color.channels = colored.channels;
            } else {
                self.color = Some(colored.clone());
            }
        } else {
            self.color = None;
        }
        self.dmx_address = file_descriptor.dmx_address;
        self.dmx_universe = file_descriptor.dmx_universe;
        self.fixturetype.clone_from(&file_descriptor.fixturetype);
        self.stage_coordinates = file_descriptor.stage_coordinates;
        self.footprint_size = file_descriptor.footprint_size;
        if let Some(movement_channels) = &file_descriptor.movement_channels {
            if let Some(existing_movement_channels) =
                &mut self.movement_channels
            {
                if let Some(pan) = movement_channels.pan {
                    if let Some(mut existing_pan) =
                        existing_movement_channels.pan
                    {
                        existing_pan.channel = pan.channel;
                    } else {
                        existing_movement_channels.pan = Some(pan);
                    }
                }
                if let Some(tilt) = movement_channels.tilt {
                    if let Some(mut existing_tilt) =
                        existing_movement_channels.tilt
                    {
                        existing_tilt.channel = tilt.channel;
                    } else {
                        existing_movement_channels.tilt = Some(tilt);
                    }
                }
            } else {
                self.movement_channels = Some(movement_channels.clone());
            }
        } else {
            self.movement_channels = None;
        }

        for new_channel in &file_descriptor.unimplemented_channels {
            if let Some(existing_channel) =
                self.unimplemented_channels.iter_mut().find(|ch| {
                    ch.name == new_channel.name
                        && ch.channel == new_channel.channel
                })
            {
                existing_channel.id = new_channel.id;
                existing_channel.dimmable = new_channel.dimmable;
                existing_channel.inverted = new_channel.inverted;
                existing_channel.default = new_channel.default;
                existing_channel.keypoints = new_channel.keypoints.clone();
            } else {
                let mut new_unimplemented_channel = new_channel.clone();
                new_unimplemented_channel.value =
                    new_unimplemented_channel.default;
                self.unimplemented_channels.push(new_unimplemented_channel);
            }
        }

        // Remove channels that no longer exist in the file descriptor
        self.unimplemented_channels.retain(|existing_channel| {
            file_descriptor
                .unimplemented_channels
                .iter()
                .any(|new_channel| {
                    new_channel.name == existing_channel.name
                        && new_channel.channel == existing_channel.channel
                })
        });
    }
    #[must_use]
    #[allow(clippy::missing_const_for_fn)]
    pub fn get_name(&self) -> &str {
        &self.name
    }
    #[must_use]
    pub const fn get_type(&self) -> &String {
        &self.fixturetype
    }
    pub fn set_pan_tilt_position(&mut self, position: &FixturePosition) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.set_pan(position.pan().into());
            movement_channels.set_tilt(position.tilt().into());
        }
    }
    pub const fn set_position_index_offset_mode(
        &mut self,
        position_index_offset_mode: PositionIndexOffsetMode,
    ) {
        self.position_index_offset_mode = position_index_offset_mode;
    }
    pub fn apply_blueprint(
        &mut self,
        blueprint: &BlueprintFileDescriptor,
        oneshot: bool,
        all_positions: &[ComposedPanTiltPosition],
    ) {
        // TODO: test if this is correct
        self.index = 0.;

        self.snippet_calls_of_current_timecode = vec![];

        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.extract_internal_spline_from_positions_in(
                &blueprint.properties,
                all_positions,
                self.id,
                Some(blueprint.id),
                oneshot,
            );
            movement_channels.extract_internal_spline_from(
                &blueprint.properties,
                oneshot,
                Some(blueprint.id),
            );
        }

        if let Some(ref mut color) = self.color {
            color.extract_internal_spline_from(
                &blueprint.properties,
                oneshot,
                Some(blueprint.id),
            );
        }

        for unimplemented_channel in &mut self.unimplemented_channels {
            unimplemented_channel.extract_internal_spline_from(
                &blueprint.properties,
                oneshot,
                Some(blueprint.id),
            );
        }
        #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
        if let Some(delay_by_eights) = blueprint
            .registered_fixture_delays
            .iter()
            .find(|blueprint_fx_id| blueprint_fx_id.fixture_id == self.id)
        {
            self.blueprint_delay_eights = delay_by_eights.delay_eights as f32;
        } else {
            self.blueprint_delay_eights = 0.;
        }
        self.blueprint_random_delay = random::<u8>().into();
    }
    pub fn apply_timecode(
        &mut self,
        timecode: &TimecodeFileDescriptor,
        dmx_renderer: &mut DbHandler,
    ) {
        dmx_renderer.request_snippet_state_reset();

        let all_properties = all_properties_from_tracks(&timecode.tracks);

        let mut snippets_in_timecode = vec![];
        for snippet in &all_properties {
            if let TimedPropertyFileDescriptor::CallSnippet(
                snippet_id,
                x_offset,
                fixture_ids,
            ) = snippet
            {
                if fixture_ids.contains(&self.id) {
                    #[allow(
                        clippy::cast_precision_loss,
                        clippy::as_conversions
                    )]
                    snippets_in_timecode.push(SnippetCall {
                        x: (*x_offset) as f32,
                        snippet_id: *snippet_id,
                    });
                }
            }
        }
        snippets_in_timecode.sort_by(|a, b| a.x.total_cmp(&b.x));
        self.snippet_calls_of_current_timecode = snippets_in_timecode;

        let merged_properties =
            merge_by_type_for_fixture(&all_properties, self.id);

        if let Some(ref mut color) = self.color {
            color.extract_internal_spline_from(&merged_properties, true, None);
        }

        for unimplemented_channel in &mut self.unimplemented_channels {
            unimplemented_channel.extract_internal_spline_from(
                &merged_properties,
                true,
                None,
            );
        }
    }
    pub fn pan_to(&mut self, new_pan: usize) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.set_pan(new_pan);
            movement_channels.set_pan_origin(new_pan);
        }
    }
    pub fn add_to_pan(&mut self, modifier: usize) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.add_to_pan(modifier);
        }
    }
    pub fn tilt_to(&mut self, new_tilt: usize) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.set_tilt(new_tilt);
            movement_channels.set_tilt_origin(new_tilt);
        }
    }
    pub fn add_to_tilt(&mut self, modifier: usize) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.add_to_tilt(modifier);
        }
    }
    pub fn set_pan_origin(&mut self, origin: usize) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.set_pan_origin(origin);
        }
    }
    pub fn set_tilt_origin(&mut self, origin: usize) {
        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.set_tilt_origin(origin);
        }
    }
    #[must_use]
    pub fn color(&self) -> Vec<RgbColor> {
        self.color
            .iter()
            .map(|color_channel| color_channel.color)
            .collect()
    }
    #[allow(clippy::cast_possible_truncation, clippy::as_conversions)]
    pub fn set_color(
        &mut self,
        rgb_color: &RgbColor,
        fade_duration_beat_count: Option<usize>,
    ) {
        #[allow(clippy::cast_precision_loss)]
        if let Some(ref mut color) = self.color {
            if let Some(fade_duration_beat_count) = fade_duration_beat_count {
                color.create_spline_from_current_to_target_color(
                    *rgb_color,
                    fade_duration_beat_count,
                    self.index,
                );
            } else {
                color.color = *rgb_color;
                color.clear_all_splines();
            }
        }
    }
    #[allow(
        clippy::cast_precision_loss,
        clippy::as_conversions,
        clippy::cast_sign_loss,
        clippy::cast_possible_truncation
    )]
    pub fn set_dimmer_value(&mut self, new_dimmer_value: usize) {
        self.dimmer_value = new_dimmer_value
            .clamp(*DIMMER_RANGE.start() as usize, *DIMMER_RANGE.end() as usize)
            as f32
            / 100.;
    }
    #[allow(
        clippy::cast_precision_loss,
        clippy::as_conversions,
        clippy::cast_sign_loss,
        clippy::cast_possible_truncation
    )]
    pub fn set_speed_of_blueprints(&mut self, speed: usize) {
        self.speed_of_blueprints = (speed as f32 / 100.).clamp(
            *BLUEPRINT_SPEED_RANGE.start(),
            *BLUEPRINT_SPEED_RANGE.end(),
        );
    }
    #[allow(
        clippy::cast_precision_loss,
        clippy::as_conversions,
        clippy::cast_sign_loss,
        clippy::cast_possible_truncation
    )]
    pub fn set_blueprint_intensity(&mut self, intensity: usize) {
        self.blueprint_intensity = (intensity as f32 / 100.).clamp(
            *BLUEPRINT_INTENSITY_RANGE.start(),
            *BLUEPRINT_INTENSITY_RANGE.end(),
        );
    }
    #[must_use]
    #[allow(
        clippy::cast_possible_truncation,
        clippy::as_conversions,
        clippy::cast_sign_loss
    )]
    pub const fn get_blueprint_speed(&self) -> usize {
        (self.speed_of_blueprints * 100.) as usize
    }
    #[must_use]
    #[allow(
        clippy::cast_possible_truncation,
        clippy::as_conversions,
        clippy::cast_sign_loss
    )]
    pub const fn get_blueprint_intensity(&self) -> usize {
        (self.blueprint_intensity * 100.) as usize
    }
    #[must_use]
    pub const fn get_stage_coordinates(&self) -> (usize, usize) {
        self.stage_coordinates
    }
    pub const fn set_stage_coordinates(&mut self, coordinates: (usize, usize)) {
        self.stage_coordinates = coordinates;
    }
    #[must_use]
    pub const fn get_enabled(&self) -> bool {
        self.enabled
    }
    pub const fn set_enabled(&mut self, value: bool) {
        self.enabled = value;
    }
    pub fn set_unimplemented_channel(
        &mut self,
        name: &String,
        value: usize,
        fade_duration_beat_count: usize,
    ) {
        for channel in &mut self.unimplemented_channels {
            if channel.name() == *name {
                channel.set_value(
                    value,
                    fade_duration_beat_count,
                    Self::spline_index_from_nanos(self.index),
                );
            }
        }
    }
    pub fn reset_unimplemented_channel(
        &mut self,
        name: &String,
        fade_duration_beat_count: usize,
    ) {
        for channel in &mut self.unimplemented_channels {
            if *channel.name() == *name {
                channel.set_to_default(fade_duration_beat_count, self.index);
            }
        }
    }
    pub fn reset_all_channels_to_defaults(&mut self) {
        for channel in &mut self.unimplemented_channels {
            channel.set_to_default(0, self.index);
        }

        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels.reset_to_defaults();
        }

        if let Some(color) = self.color.as_mut() {
            color.reset_to_default();
        }
    }
    pub fn update_all_blueprint_position_indices(&mut self) {
        let index = Self::spline_index_from_nanos(
            // TODO: investigate. This might be broken, but i dont know
            match self.position_index_offset_mode {
                PositionIndexOffsetMode::Matching => self.index,
                PositionIndexOffsetMode::CustomOffset => {
                    #[allow(clippy::suboptimal_flops)]
                    {
                        self.index - self.blueprint_delay_eights * 1_000.
                    }
                }
                #[allow(clippy::suboptimal_flops)]
                PositionIndexOffsetMode::Random => {
                    self.index - self.blueprint_random_delay * 1_000.
                }
                PositionIndexOffsetMode::StageUpToDown
                | PositionIndexOffsetMode::StageDownToUp
                | PositionIndexOffsetMode::StageLeftToRight
                | PositionIndexOffsetMode::StageRightToLeft => 0.,
            },
        );

        if let Some(ref mut color) = self.color {
            color.apply_spline_index(index, self.blueprint_intensity);
        }

        if let Some(movement_channels) = self.movement_channels.as_mut() {
            movement_channels
                .apply_spline_index(index, self.blueprint_intensity);
        }

        for unimplemented_channel in &mut self.unimplemented_channels {
            unimplemented_channel
                .apply_spline_index(index, self.blueprint_intensity);
        }
    }
    // pub fn update_all_timecode_position_indices(
    //     &mut self,
    //     index: f32,
    //     db_handler: &mut DbHandler,
    //     input_parser: &mut InputParser,
    // ) {
    //     // logging::debug(format!("{index:?}"));
    //     let snippet_call_index = self
    //         .snippet_calls_of_current_timecode
    //         .iter()
    //         .rposition(|snippet_call| snippet_call.x < index);
    //     if let Some(snippet_call_index) = snippet_call_index {
    //         for snippet_call in self
    //             .snippet_calls_of_current_timecode
    //             .drain(..=snippet_call_index)
    //         {
    //             if let Some(snippet) =
    //                 db_handler.find_snippet_by_id(snippet_call.snippet_id)
    //             {
    //                 input_parser.push_one_time_instructions(
    //                     &snippet.instructions,
    //                     PRESSED,
    //                 );
    //             }
    //         }
    //     }
    //
    //     if let Some(ref mut color) = self.color {
    //         color.apply_spline_index(index);
    //     }
    //
    //     if let Some(movement_channels) = self.movement_channels.as_mut() {
    //         movement_channels.apply_spline_index(index);
    //     }
    //
    //     for unimplemented_channel in &mut self.unimplemented_channels {
    //         unimplemented_channel.apply_spline_index(index);
    //     }
    // }
    pub fn update_spline_index(
        &mut self,
        position_index: Option<f32>,
        delta: Option<(Duration, Duration)>,
    ) {
        if delta.is_none() {
            if let Some(position_index) = position_index {
                self.index = position_index;
            } else {
                logging::log("`delta` and `position_index` cannot be both *None* when updating the index on fixtures".to_owned(), logging::LogLevel::Warning, false);
            }
        } else if position_index.is_none() {
            if let Some((delta, beat_duration)) = delta {
                if beat_duration.is_zero() {
                    return;
                }
                let speed_multiplier = self.speed_of_blueprints;
                #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
                let delta = (delta.as_nanos() as f32)
                    * (1000. / beat_duration.as_millis() as f32);
                self.index += delta * speed_multiplier;
            } else {
                logging::log("`delta` and `position_index` cannot be both *None* when updating the index on fixtures".to_owned(), logging::LogLevel::Warning, false);
            }
        } else {
            logging::log("`delta` and `position_index` cannot both be *Some* when updating the index on fixtures".to_owned(), logging::LogLevel::Warning, false);
        }
        self.update_all_blueprint_position_indices();
    }
    pub fn get_dmx_footprint(
        &mut self,
        _db_handler: &mut DbHandler,
        _input_parser: &mut InputParser,
    ) -> Vec<u8> {
        // self.update_all_timecode_position_indices(
        //     self.index,
        //     db_handler,
        //     input_parser,
        // );

        let mut footprint = vec![0_u8; self.footprint_size];

        let mut dmx_channel_emitter: Vec<Box<dyn DmxChannelEmitter>> = vec![];

        if let Some(color) = self.color.clone() {
            dmx_channel_emitter.push(Box::new(color));
        }

        if let Some(movement_channels) = self.movement_channels.clone() {
            dmx_channel_emitter.push(Box::new(movement_channels));
        }
        for unimplemented_channel in self.unimplemented_channels.clone() {
            dmx_channel_emitter.push(Box::new(unimplemented_channel));
        }
        for dmx_channel_emitter in &mut dmx_channel_emitter {
            let dmx_channels =
                dmx_channel_emitter.compute_dmx_channels(self.dimmer_value);
            for dmx_channel in dmx_channels {
                if let Some(channel) =
                    footprint.get_mut(dmx_channel.channel.saturating_sub(1))
                {
                    *channel = dmx_channel.value;
                }
            }
        }
        footprint
    }
    #[must_use]
    pub const fn get_timecode_dmx_footprint(&self) -> Vec<u8> {
        vec![]
    }
    #[must_use]
    pub const fn dmx_address(&self) -> Option<usize> {
        self.dmx_address
    }
    #[must_use]
    pub const fn dmx_universe(&self) -> u16 {
        self.dmx_universe
    }
    #[must_use]
    pub const fn get_footprint_size(&self) -> usize {
        self.footprint_size
    }
    #[must_use]
    pub fn unimplemented_channels(&self) -> Vec<&UnimplementedChannel> {
        self.unimplemented_channels.iter().collect()
    }
    #[must_use]
    pub fn get_function_value(&self, channel: &str) -> Option<u8> {
        match channel {
            "pan" => Some(
                self.movement_channels
                    .clone()?
                    .pan_tilt()
                    .0
                    .try_into()
                    .unwrap_or(u8::MAX),
            ),
            "tilt" => Some(
                self.movement_channels
                    .clone()?
                    .pan_tilt()
                    .1
                    .try_into()
                    .unwrap_or(u8::MAX),
            ),
            "red" => Some(
                self.color
                    .clone()
                    .map(|color| color.color.red)
                    .unwrap_or_default(),
            ),
            "green" => Some(
                self.color
                    .clone()
                    .map(|color| color.color.green)
                    .unwrap_or_default(),
            ),
            "blue" => Some(
                self.color
                    .clone()
                    .map(|color| color.color.blue)
                    .unwrap_or_default(),
            ),
            channel => {
                let unimplemented_channel = self
                    .unimplemented_channels
                    .iter()
                    .find(|unimplemented_channel| {
                        unimplemented_channel.name() == channel
                    })?;
                Some(unimplemented_channel.value())
            }
        }
    }
    pub fn clear_all_splines(&mut self) {
        if let Some(ref mut color) = self.color {
            color.clear_all_splines();
        }
        if let Some(ref mut movement_channels) = self.movement_channels {
            movement_channels.clear_all_splines();
        }
        for unimplemented_channel in &mut self.unimplemented_channels {
            unimplemented_channel.clear_all_splines();
        }
    }
    pub fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize) {
        if let Some(ref mut color) = self.color {
            color.clear_splines_by_blueprint_id(blueprint_id);
        }
        if let Some(ref mut movement_channels) = self.movement_channels {
            movement_channels.clear_splines_by_blueprint_id(blueprint_id);
        }
        for unimplemented_channel in &mut self.unimplemented_channels {
            unimplemented_channel.clear_splines_by_blueprint_id(blueprint_id);
        }
    }
    /// Our index is in nanoseconds, but our splines are in milliseconds
    #[must_use]
    pub fn spline_index_from_nanos(index: f32) -> f32 {
        index / 1_000_000.
    }
}
