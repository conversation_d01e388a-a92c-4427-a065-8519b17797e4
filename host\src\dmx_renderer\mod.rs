use crate::database_handler::pan_tilt_positions::{
    ComposedPanTiltPosition, FixturePosition,
};
use crate::database_handler::<PERSON>b<PERSON><PERSON><PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::fixture::DmxFixture;
use crate::input_parser::structs::{
    ActivatedFixture, FunctionGetter, InputParser,
};
use crate::logging;
use serde::{Deserialize, Serialize};
use splines::Interpolation;

pub mod channel;
pub mod dynamics;
pub mod fixture;
pub mod output;

use channel::unimplemented_channels::Keypoint;
use core::fmt::Display;
use core::time::Duration;
use dynamics::timecode::TimecodeFileDescriptor;
use output::ArtNet;
use ts_rs::TS;

use self::dynamics::blueprint::BlueprintFileDescriptor;
use self::fixture::{DmxFixtureFileDescriptor, PositionIndexOffsetMode};

const UNIVERSE_SIZE: usize = 512;
pub const DMX_FRAME_RATE_IN_MS: u64 = 30; // ca. 25 FPS

pub const DEFAULT_BPM: usize = 125;

pub struct DmxRenderer {
    pub bpm_modifier: f32,
    bpm: usize,
    fixtures: Vec<DmxFixture>,
    artnet_output: Option<ArtNet>,
}

impl DmxRenderer {
    #[must_use]
    pub fn new(db_handler: &mut DbHandler) -> Self {
        let artnet_output = ArtNet::new();

        let fixtures = DbHandler::fixtures_for_active_show(
            &mut db_handler.db_connection(),
        )
        .iter()
        .map(|dmx_fixture_file_descriptor| {
            DmxFixture::new(dmx_fixture_file_descriptor.clone())
        })
        .collect();

        Self {
            fixtures,
            bpm: DEFAULT_BPM,
            bpm_modifier: 1.,
            artnet_output,
        }
    }
    pub fn render(
        &mut self,
        db_handler: &mut DbHandler,
        input_parser: &mut InputParser,
        delta: (Duration, Duration),
    ) {
        let mut universes: Vec<(u16, Vec<u8>)> = vec![];
        let mut existing_universes: Vec<u16> =
            self.fixtures.iter().map(DmxFixture::dmx_universe).collect();
        existing_universes.sort_unstable();
        existing_universes.dedup();
        for universe in &existing_universes {
            universes.push((
                *universe,
                self.build_dmx_universe(
                    *universe,
                    db_handler,
                    input_parser,
                    delta,
                ),
            ));
        }
        if let Some(artnet_output) = self.artnet_output.as_ref() {
            artnet_output.send(&universes);
        }
    }
    pub fn build_dmx_universe(
        &mut self,
        universe: u16,
        db_handler: &mut DbHandler,
        input_parser: &mut InputParser,
        delta: (Duration, Duration),
    ) -> Vec<u8> {
        let mut dmx_values: Vec<u8> = vec![0; UNIVERSE_SIZE];
        let fixtures_of_universe = self
            .fixtures
            .iter_mut()
            .filter(|fixture| fixture.dmx_universe() == universe);
        for fixture in fixtures_of_universe {
            let Some(dmx_address) = fixture.dmx_address() else {
                continue;
            };
            fixture.update_spline_index(None, Some((delta.0, delta.1)));
            for (i, channel) in fixture
                .get_dmx_footprint(db_handler, input_parser)
                .iter()
                .enumerate()
            {
                if let Some(t) = dmx_values
                    .get_mut(i.saturating_add(dmx_address.saturating_sub(1)))
                {
                    *t = *channel;
                }
            }
        }
        // logging::debug(format!("{:?}", dmx_values.get(..8)));
        dmx_values
    }
    pub fn apply_blueprint(
        &mut self,
        fixtures: &[ActivatedFixture],
        oneshot: bool,
        blueprint: &BlueprintFileDescriptor,
        all_positions: &[ComposedPanTiltPosition],
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.apply_blueprint(blueprint, oneshot, all_positions);
        });
    }
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub fn apply_timecode(
        &mut self,
        fixtures: &[ActivatedFixture],
        timecode: &TimecodeFileDescriptor,
        _index: usize,
        _timecode_advancing: bool,
        db_handler: &mut DbHandler,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.apply_timecode(timecode, db_handler);
        });
    }
    #[must_use]
    pub const fn is_artnet_active(&self) -> bool {
        self.artnet_output.is_some()
    }
    pub fn merge_new_fixtures_with_state(
        &mut self,
        fixtures: Vec<DmxFixtureFileDescriptor>,
    ) {
        self.fixtures.retain(|dmx_fixture| {
            fixtures
                .iter()
                .find(|fixture_fd| fixture_fd.id == Some(dmx_fixture.id()))
                .is_some()
        });
        for dmx_fixture_file_descriptor in fixtures {
            let Some(dmx_fixture_file_descriptor_id) =
                dmx_fixture_file_descriptor.id
            else {
                logging::log(
                    "Failing to merge DmxFixtureFD without an id with state"
                        .to_owned(),
                    logging::LogLevel::Warning,
                    true,
                );
                return;
            };
            let Some(fixture) =
                self.get_fixture_mut(dmx_fixture_file_descriptor_id)
            else {
                self.fixtures
                    .push(DmxFixture::new(dmx_fixture_file_descriptor.clone()));
                continue;
            };
            fixture
                .merge_file_descriptor_with_state(&dmx_fixture_file_descriptor);
        }
    }
    pub const fn get_fixtures(&mut self) -> &Vec<DmxFixture> {
        &self.fixtures
    }
    fn get_fixture_mut(&mut self, id: usize) -> Option<&mut DmxFixture> {
        self.fixtures.iter_mut().find(|fixture| fixture.id() == id)
    }
    pub fn set_color(
        &mut self,
        fixtures: &[ActivatedFixture],
        color: RgbColor,
        fade_duration: Option<usize>,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_color(&color, fade_duration);
        });
    }
    pub fn set_dimmer_value(
        &mut self,
        new_dimmer_value: usize,
        fixtures: &[ActivatedFixture],
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_dimmer_value(new_dimmer_value);
        });
    }
    pub fn pan_to(&mut self, fixtures: &[ActivatedFixture], new_pan: usize) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.pan_to(new_pan);
        });
    }
    pub fn add_to_pan(
        &mut self,
        fixtures: &[ActivatedFixture],
        modifier: usize,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.add_to_pan(modifier);
        });
    }
    pub fn tilt_to(&mut self, fixtures: &[ActivatedFixture], new_tilt: usize) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.tilt_to(new_tilt);
        });
    }
    pub fn add_to_tilt(
        &mut self,
        fixtures: &[ActivatedFixture],
        modifier: usize,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.add_to_tilt(modifier);
        });
    }
    pub const fn bpm_to(&mut self, new_bpm: usize) {
        self.bpm = new_bpm;
    }
    #[must_use]
    pub const fn bpm(&self) -> usize {
        self.bpm
    }
    pub const fn bpm_modifier_to(&mut self, new_modifier: f32) {
        self.bpm_modifier = new_modifier;
    }
    #[must_use]
    pub const fn bpm_modifier(&self) -> f32 {
        self.bpm_modifier
    }
    pub fn set_pan_tilt_position(
        &mut self,
        fixtures: &[ActivatedFixture],
        position: &ComposedPanTiltPosition,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            if let Some(pan_tilt_position) =
                position.fixture_positions.iter().find(|iter_position| {
                    iter_position.fixture_id() == fixture.id()
                })
            {
                fixture.set_pan_tilt_position(pan_tilt_position);
            }
        });
    }
    pub fn set_enabled(
        &mut self,
        fixtures: &[ActivatedFixture],
        setter: fn(bool) -> bool,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_enabled(setter(fixture.get_enabled()));
        });
    }
    pub fn set_position_index_offset_mode(
        &mut self,
        fixtures: &[ActivatedFixture],
        position_index_offset_mode: PositionIndexOffsetMode,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_position_index_offset_mode(position_index_offset_mode);
        });
    }
    pub fn set_speed_of_blueprints(
        &mut self,
        fixtures: &[ActivatedFixture],
        speed: usize,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_speed_of_blueprints(speed);
        });
    }
    pub fn set_blueprint_intensity(
        &mut self,
        fixtures: &[ActivatedFixture],
        intensity: usize,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_blueprint_intensity(intensity);
        });
    }
    pub fn update_enabled(
        &mut self,
        fixtures: &[ActivatedFixture],
        update_fn: fn(bool) -> bool,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_enabled(update_fn(fixture.get_enabled()));
        });
    }
    fn for_all_fixtures_in<F>(
        &mut self,
        active_fixtures: &[ActivatedFixture],
        mut callback: F,
    ) where
        F: FnMut(&mut DmxFixture),
    {
        for active_fixture in active_fixtures {
            self.fixtures.iter_mut().for_each(|fixture| {
                if active_fixture.name == fixture.get_name()
                    && active_fixture.fixturetype == *fixture.get_type()
                {
                    callback(fixture);
                }
            });
        }
    }
    pub const fn set_beat_fraction_duration(&mut self, new_bpm: usize) {
        self.bpm = new_bpm;
    }
    pub const fn set_manual_beat_duration_modifier(
        &mut self,
        new_modifier: f32,
    ) {
        self.bpm_modifier = new_modifier;
    }
    pub const fn reset_to_defaults(&mut self) {
        self.bpm = DEFAULT_BPM;
        self.bpm_modifier = 1.;
    }
    pub fn unimplemented_channel_to(
        &mut self,
        fixtures: &[ActivatedFixture],
        name: &String,
        value: Option<usize>,
        fade_duration_beat_count: Option<usize>,
    ) {
        let fade_duration_beat_count = fade_duration_beat_count.unwrap_or(0);

        self.for_all_fixtures_in(fixtures, |fixture| {
            if let Some(value) = value {
                fixture.set_unimplemented_channel(
                    name,
                    value,
                    fade_duration_beat_count,
                );
            } else {
                fixture.reset_unimplemented_channel(
                    name,
                    fade_duration_beat_count,
                );
            }
        });
    }
    pub fn reset_unimplemented_channel(
        &mut self,
        fixtures: &[ActivatedFixture],
        channel_name: &String,
        fade_duration_beat_count: Option<usize>,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.reset_unimplemented_channel(
                channel_name,
                fade_duration_beat_count.unwrap_or(0),
            );
        });
    }
    pub fn clear_all_splines(&mut self, fixtures: &[ActivatedFixture]) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.clear_all_splines();
        });
    }
    pub fn clear_splines_by_blueprint_id(
        &mut self,
        fixtures: &[ActivatedFixture],
        blueprint_id: usize,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.clear_splines_by_blueprint_id(blueprint_id);
        });
    }
    pub fn reset_all_fixtures_to_defaults(&mut self) {
        for fixture in &mut self.fixtures {
            fixture.reset_all_channels_to_defaults();
        }
    }
    pub fn capture_pan_tilt_positions(&mut self) -> Vec<FixturePosition> {
        let mut pan_tilt_positions = vec![];

        for live_fixture in self.get_fixtures() {
            if let Some(movement_channels) = live_fixture.movement_channels() {
                let (pan, tilt) = movement_channels.pan_tilt();
                pan_tilt_positions.push(FixturePosition::new(
                    live_fixture.id(),
                    pan.try_into().unwrap_or(u8::MAX),
                    tilt.try_into().unwrap_or(u8::MAX),
                ));
            }
        }
        pan_tilt_positions
    }
    #[must_use]
    pub fn all_keypoints(&self) -> Vec<&Keypoint> {
        let mut keypoints = vec![];
        for fixture in &self.fixtures {
            for channel in fixture.unimplemented_channels() {
                keypoints.push(channel.keypoints());
            }
        }
        keypoints.iter().flatten().copied().collect()
    }
    #[must_use]
    pub fn get_function_value_of_fixture(
        &self,
        function_getter: &FunctionGetter,
    ) -> Option<u8> {
        let fixture = self
            .fixtures
            .iter()
            .find(|fixture| fixture.id() == function_getter.fixture_id)?;

        fixture.get_function_value(&function_getter.function)
    }
    #[must_use]
    pub fn get_blueprint_speed_of_fixture(
        &self,
        fixture_id: usize,
    ) -> Option<usize> {
        let fixture = self
            .fixtures
            .iter()
            .find(|fixture| fixture.id() == fixture_id)?;

        #[allow(
            clippy::cast_precision_loss,
            clippy::cast_possible_truncation,
            clippy::cast_sign_loss,
            clippy::as_conversions
        )]
        Some(fixture.get_blueprint_speed())
    }
    #[must_use]
    pub fn get_blueprint_intensity_of_fixture(
        &self,
        fixture_id: usize,
    ) -> Option<usize> {
        let fixture = self
            .fixtures
            .iter()
            .find(|fixture| fixture.id() == fixture_id)?;

        Some(fixture.get_blueprint_intensity())
    }
}

#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub enum InterpolationMethod {
    Linear,
    Cosine,
}

impl<T, V> From<InterpolationMethod> for Interpolation<T, V> {
    fn from(value: InterpolationMethod) -> Self {
        match value {
            InterpolationMethod::Linear => Self::Linear,
            InterpolationMethod::Cosine => Self::Cosine,
        }
    }
}

impl From<String> for InterpolationMethod {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Cosine" => Self::Cosine,
            _ => Self::Linear,
        }
    }
}

impl Display for InterpolationMethod {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        match self {
            Self::Linear => write!(f, "Linear"),
            Self::Cosine => write!(f, "Cosine"),
        }
    }
}
