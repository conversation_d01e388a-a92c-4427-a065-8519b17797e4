use core::time::Duration;
use serde::{Deserialize, Serialize};
use ts_rs::TS;

#[derive(
    <PERSON><PERSON>, <PERSON><PERSON>, Debug, De<PERSON>ult, PartialEq, Eq, Serialize, Deserialize, TS,
)]
#[ts(export)]
pub struct BeatDuration {
    /// 8192th notes
    pub beatfractions: usize,
}

#[derive(C<PERSON>, Copy, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub enum NoteType {
    Whole,
    Half,
    Quarter,
    Eighth,
    Sixteenth,
    /// 8192th notes
    Fraction,
}

impl NoteType {
    #[must_use]
    pub const fn to_fraction(&self) -> usize {
        match self {
            Self::Whole => 8192,
            Self::Half => 4096,
            Self::Quarter => 2048,
            Self::Eighth => 1024,
            Self::Sixteenth => 512,
            // 8192th notes
            Self::Fraction => 1,
        }
    }
}

impl From<NoteType> for String {
    fn from(value: NoteType) -> Self {
        match value {
            NoteType::Whole => "Whole".to_owned(),
            NoteType::Half => "Half".to_owned(),
            NoteType::Quarter => "Quarter".to_owned(),
            NoteType::Eighth => "Eighth".to_owned(),
            NoteType::Sixteenth => "Sixteenth".to_owned(),
            NoteType::Fraction => "Fraction".to_owned(),
        }
    }
}

impl From<String> for NoteType {
    fn from(value: String) -> Self {
        #[allow(clippy::wildcard_enum_match_arm)]
        match value.as_str() {
            "Whole" => Self::Whole,
            "Half" => Self::Half,
            "Eighth" => Self::Eighth,
            "Sixteenth" => Self::Sixteenth,
            "Fraction" => Self::Fraction,
            _ => Self::Quarter, // Default to quarter note
        }
    }
}

impl BeatDuration {
    #[must_use]
    pub const fn new(note_type: NoteType, count: usize) -> Self {
        Self {
            beatfractions: note_type.to_fraction().saturating_mul(count),
        }
    }

    #[must_use]
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub const fn as_wholes(&self) -> f32 {
        self.beatfractions as f32 / NoteType::Whole.to_fraction() as f32
    }

    #[must_use]
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub const fn as_halves(&self) -> f32 {
        self.beatfractions as f32 / NoteType::Half.to_fraction() as f32
    }

    #[must_use]
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub const fn as_quarters(&self) -> f32 {
        self.beatfractions as f32 / NoteType::Quarter.to_fraction() as f32
    }

    #[must_use]
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub const fn as_eighths(&self) -> f32 {
        self.beatfractions as f32 / NoteType::Eighth.to_fraction() as f32
    }

    #[must_use]
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub const fn as_sixteenths(&self) -> f32 {
        self.beatfractions as f32 / NoteType::Sixteenth.to_fraction() as f32
    }

    #[must_use]
    pub const fn as_fractions(&self) -> usize {
        self.beatfractions
    }

    #[allow(
        clippy::as_conversions,
        clippy::cast_precision_loss,
        clippy::cast_possible_truncation,
        clippy::cast_sign_loss
    )]
    #[must_use]
    pub fn as_duration(&self, bpm: usize, bpm_modifier: f32) -> Duration {
        let total_duration_nanos =
            Self::beat_fraction_duration(bpm, bpm_modifier).as_micros() as f32
                * self.beatfractions as f32;

        Duration::from_micros(
            total_duration_nanos.round().clamp(0.0, u64::MAX as f32) as u64,
        )
    }

    /// Length of a quarter note (beat duration)
    /// Calculate `* <tact>` to get the duration of a full bar
    /// Calculate `/ 2` to get the duration of an eighth
    #[allow(
        clippy::as_conversions,
        clippy::cast_precision_loss,
        clippy::cast_possible_truncation,
        clippy::cast_sign_loss
    )]
    #[must_use]
    pub fn beat_duration(bpm: usize, bpm_modifier: f32) -> Duration {
        Duration::from_millis(
            (60000.0
                / ((bpm.clamp(1, f32::MAX as usize) as f32) * bpm_modifier))
                .round()
                .clamp(0.0, u64::MAX as f32) as u64,
        )
    }

    #[must_use]
    pub fn beat_fraction_duration(bpm: usize, bpm_modifier: f32) -> Duration {
        Duration::from_nanos(
            Self::beat_duration(bpm, bpm_modifier)
                .as_nanos()
                .checked_div(
                    u128::try_from(NoteType::Quarter.to_fraction())
                        .unwrap_or(u128::MAX),
                )
                .unwrap_or(u128::MIN)
                .try_into()
                .unwrap_or(u64::MAX),
        )
    }

    #[must_use]
    pub const fn is_zero(&self) -> bool {
        self.beatfractions == 0
    }
}

impl core::ops::Add for BeatDuration {
    type Output = Self;

    fn add(self, other: Self) -> Self::Output {
        Self {
            beatfractions: self
                .beatfractions
                .saturating_add(other.beatfractions),
        }
    }
}

impl core::ops::Sub for BeatDuration {
    type Output = Self;

    fn sub(self, other: Self) -> Self::Output {
        Self {
            beatfractions: self
                .beatfractions
                .saturating_sub(other.beatfractions),
        }
    }
}
