use crate::logging;

use alloc::sync::Arc;
use core::time::Duration;
use std::net::UdpSocket;
use std::thread;
use tokio::sync::Mutex;

/// The int describes, how many fractions a beat can have
/// The decimals are a modifyer to account for network latency and stuff
pub const BEAT_FRACTION_DIVIDER: f64 = 8.01;

pub fn spawn_beat_receive_thread(
    beat_index_clone: Arc<Mutex<(u8, bool)>>,
    last_calculated_bpm_clone: Arc<Mutex<usize>>,
    time_since_last_beat_clone: Arc<Mutex<i32>>,
) {
    if let Ok(e) = UdpSocket::bind("0.0.0.0:50001") {
        let socket = e;
        match socket.set_read_timeout(Some(Duration::from_millis(20_000))) {
            Ok(()) => (),
            Err(_) => logging::log(
                format!("Error while setting read_timeout for UdpSocket"),
                logging::LogLevel::Warning,
                true,
            ),
        }
        // here are all packets from the pioneer cdj: https://djl-analysis.deepsymmetry.org/djl-analysis/packets.html
        thread::spawn(move || {
            logging::log(
                format!("Started listening for beat packets on network broadcast port 50001"),
                logging::LogLevel::Info,
                false,
            );
            loop {
                let mut buf = [0; 93];
                let Ok(mut time_since_last_beat_locked) =
                    time_since_last_beat_clone.try_lock()
                else {
                    continue;
                };
                if socket.recv_from(&mut buf).is_ok() {
                    if *time_since_last_beat_locked > 0 {
                        logging::log(
                            format!("Beat detected"),
                            logging::LogLevel::Info,
                            true,
                        );
                    }
                    *time_since_last_beat_locked = 1;
                } else {
                    *time_since_last_beat_locked =
                        time_since_last_beat_locked.saturating_add(20);
                    if *time_since_last_beat_locked < 120
                        || *time_since_last_beat_locked % 1000 == 0
                    {
                        logging::log(
                            format!(
                                "No beat was detected on the network for {}",
                                if *time_since_last_beat_locked < 120 {
                                    format!(
                                        "{} seconds",
                                        *time_since_last_beat_locked
                                    )
                                } else {
                                    format!(
                                        "{} minutes",
                                        *time_since_last_beat_locked / 60
                                    )
                                }
                            ),
                            logging::LogLevel::Warning,
                            true,
                        );
                    }
                    continue;
                }
                if *time_since_last_beat_locked == 0 {
                    if let Ok(mut e) = last_calculated_bpm_clone.try_lock() {
                        *e = calculate_bpm_from_buf(buf);
                        // if packet contains beat number 1, the position index will be reset to truly sync to the beat
                        if buf[92] == 1 {
                            match beat_index_clone.try_lock() {
                                Ok(mut e) => *e = (2, false),
                                Err(_) => (),
                            };
                        }
                    }
                }
            }
        });
    } else {
        logging::log(format!(
            "Error while binding to UdpSocket for port 50001 (used to listen for beat packets from Pinoeer dj equipment)"),
            logging::LogLevel::Warning,
            true
        );
    }
}
// * calculates the current time between beats with its modifyer
#[allow(
    clippy::cast_sign_loss,
    clippy::cast_possible_truncation,
    clippy::as_conversions
)]
fn calculate_bpm_from_buf(buf: [u8; 93]) -> usize {
    (((
            // calculate bpm
            f64::from(buf[90]).mul_add(256.0, f64::from(buf[91]))
        )
        *
        (
            // calculate modifyer
            f64::from(buf[85]).mul_add(65536.0, f64::from(buf[86])).mul_add(256.0, f64::from(buf[87]))
        )
    )
    // this number is 2^some
    / 104_857_600.0).clamp(0., u16::MAX.into()) as usize
}
