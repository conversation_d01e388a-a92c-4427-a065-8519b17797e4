use alloc::sync::Arc;
use tokio::sync::Mutex;

use map_to_range::MapRange;

use super::beatduration::BeatDuration;
use super::structs::{
    ActivatedFixture, ColorToInstruction, Comparator, InputParser, Instruction,
    QueuedInstruction,
};
use crate::database_handler::DbHand<PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::dynamics::blueprint::BlueprintFileDescriptor;
use crate::dmx_renderer::fixture::{
    PositionIndexOffsetMode, BLUEPRINT_INTENSITY_RANGE, BLUEPRINT_SPEED_RANGE,
};
use crate::dmx_renderer::{DmxRenderer, DMX_FRAME_RATE_IN_MS};
use crate::input_parser::structs::InstructionValue;
use crate::logging;

pub const PRESSED: usize = 1;
pub const RELEASED: usize = 0;
pub const DEFAULT_BPM: usize = 120;
pub const DEFAULT_BPM_MODIFIER: f32 = 1.;

#[allow(clippy::too_many_arguments)]
pub async fn evaluate_instructions(
    input_parser: &Arc<Mutex<InputParser>>,
    dmx_renderer: &Arc<Mutex<DmxRenderer>>,
    value: usize,
    instructions: &Vec<Instruction>,
    db_handler: &Arc<Mutex<DbHandler>>,
    scope: Option<usize>,
    selected_fixtures: &mut Vec<ActivatedFixture>,
) {
    for instruction in instructions {
        evaluate_instruction(
            input_parser,
            dmx_renderer,
            value,
            instruction,
            db_handler,
            scope,
            selected_fixtures,
        )
        .await;
    }
}

#[allow(clippy::too_many_lines, clippy::cognitive_complexity)]
pub async fn evaluate_instruction(
    input_parser: &Arc<Mutex<InputParser>>,
    dmx_renderer: &Arc<Mutex<DmxRenderer>>,
    value: usize,
    instruction: &Instruction,
    db_handler: &Arc<Mutex<DbHandler>>,
    scope: Option<usize>,
    selected_fixtures: &mut Vec<ActivatedFixture>,
) {
    match &instruction {
        Instruction::ExecuteCallableSnippet(snippet_id) => {
            let mut instructions = vec![];
            if let Some(snippet) =
                &db_handler.lock().await.find_snippet_by_id(*snippet_id)
            {
                instructions = snippet.instructions.clone();
            }
            Box::pin(evaluate_instructions(
                input_parser,
                dmx_renderer,
                value,
                &instructions,
                db_handler,
                scope,
                selected_fixtures,
            ))
            .await;
        }
        Instruction::FixtureLoop(fixture_loop) => {
            Box::pin(evaluate_instructions(
                input_parser,
                dmx_renderer,
                value,
                &fixture_loop.fixtures,
                db_handler,
                scope,
                selected_fixtures,
            ))
            .await;

            Box::pin(evaluate_instructions(
                input_parser,
                dmx_renderer,
                value,
                &fixture_loop.instructions,
                db_handler,
                scope,
                selected_fixtures,
            ))
            .await;

            Box::pin(evaluate_instruction(
                input_parser,
                dmx_renderer,
                value,
                &Instruction::DeselectAllFixtures,
                db_handler,
                scope,
                selected_fixtures,
            ))
            .await;
        }
        Instruction::DeselectAllFixtures => {
            *selected_fixtures = vec![];
        }
        Instruction::AddToGroup(add_to_group) => {
            Box::pin(evaluate_instructions(
                input_parser,
                dmx_renderer,
                value,
                &add_to_group.fixtures,
                db_handler,
                scope,
                selected_fixtures,
            ))
            .await;

            let mut db_handler = db_handler.lock().await;

            let Some(group) = db_handler
                .active_show
                .groups
                .iter_mut()
                .find(|group| group.name == add_to_group.name)
            else {
                *selected_fixtures = vec![];
                logging::log(
                    format!(
                        "Unable to find fixture group {}",
                        add_to_group.name
                    ),
                    logging::LogLevel::Warning,
                    false,
                );
                return;
            };

            if group.id.is_some() {
                for selected_fixture in &*selected_fixtures {
                    group.fixture_ids.retain(|group_fixture_id| {
                        *group_fixture_id != selected_fixture.id
                    });
                }
                for selected_fixture in selected_fixtures {
                    group.fixture_ids.push(selected_fixture.id);
                }
            } else {
                drop(db_handler);
                logging::log(
                    format!(
                        "Attempt to mutate generated group {}",
                        add_to_group.name
                    ),
                    logging::LogLevel::Warning,
                    false,
                );
            }
        }
        Instruction::RemoveFromGroup(remove_from_group) => {
            Box::pin(evaluate_instructions(
                input_parser,
                dmx_renderer,
                value,
                &remove_from_group.fixtures,
                db_handler,
                scope,
                selected_fixtures,
            ))
            .await;

            let mut db_handler = db_handler.lock().await;

            let Some(group) = db_handler
                .active_show
                .groups
                .iter_mut()
                .find(|group| group.name == remove_from_group.name)
            else {
                *selected_fixtures = vec![];
                logging::log(
                    format!(
                        "Unable to find fixture group {}",
                        remove_from_group.name
                    ),
                    logging::LogLevel::Warning,
                    false,
                );
                return;
            };

            if group.id.is_some() {
                for selected_fixture in &*selected_fixtures {
                    group.fixture_ids.retain(|group_fixture_id| {
                        *group_fixture_id != selected_fixture.id
                    });
                }
            } else {
                logging::log(
                    format!(
                        "Attempt to mutate generated group {}",
                        remove_from_group.name
                    ),
                    logging::LogLevel::Warning,
                    false,
                );
            }

            drop(db_handler);
            *selected_fixtures = vec![];
        }
        Instruction::ToggleQueueMode(queue_mode) => {
            input_parser.lock().await.queue_mode = *queue_mode;
        }
        Instruction::ColorTo(color_to_instruction) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;
            color_to(
                value,
                &input_parser,
                &mut dmx_renderer,
                color_to_instruction,
                scope,
                selected_fixtures,
            );
        }
        Instruction::ColorToRandom(fade_duration) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;
            color_to_random(
                value,
                &input_parser,
                &mut dmx_renderer,
                fade_duration.clone(),
                scope,
                selected_fixtures,
            );
        }
        Instruction::ActivateAllFixtures => {
            let db_handler = db_handler.lock().await;
            activate_all_fixtures(&db_handler, selected_fixtures);
        }
        Instruction::ActivateFixtureById(fixture_id) => {
            let db_handler = db_handler.lock().await;
            activate_fixture_by_id(&db_handler, *fixture_id, selected_fixtures);
        }
        Instruction::ActivateFixtureGroup(group_name) => {
            let db_handler = db_handler.lock().await;
            activate_fixture_group(&db_handler, group_name, selected_fixtures);
        }
        Instruction::SetBlueprintPositionIndexOffsetMode(mode) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            set_blueprint_position_index_offset_mode(
                *mode,
                &mut dmx_renderer,
                selected_fixtures,
            );
        }
        Instruction::SetSpeedOfBlueprints(speed_value) => {
            if let Some(speed_value) = speed_value {
                let mut dmx_renderer = dmx_renderer.lock().await;
                let input_parser = input_parser.lock().await;

                #[allow(
                    clippy::as_conversions,
                    clippy::cast_possible_truncation,
                    clippy::cast_sign_loss
                )]
                let speed = speed_value
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(*BLUEPRINT_SPEED_RANGE.end() as usize);

                dmx_renderer.set_speed_of_blueprints(selected_fixtures, speed);
            }
        }
        Instruction::SetBlueprintIntensity(intensity_value) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(intensity_value) = intensity_value {
                #[allow(
                    clippy::as_conversions,
                    clippy::cast_possible_truncation,
                    clippy::cast_sign_loss
                )]
                let intensity = intensity_value
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(*BLUEPRINT_INTENSITY_RANGE.end() as usize);

                dmx_renderer
                    .set_blueprint_intensity(selected_fixtures, intensity);
            }
        }
        Instruction::BlueprintTo(blueprint_to_instruction) => {
            let (bpm, bpm_modifier, beatduration) = {
                let mut db_handler = db_handler.lock().await;
                let mut dmx_renderer = dmx_renderer.lock().await;

                blueprint_to(
                    blueprint_to_instruction.id,
                    blueprint_to_instruction.oneshot,
                    &mut dmx_renderer,
                    &db_handler,
                    selected_fixtures,
                );

                (
                    dmx_renderer.bpm(),
                    dmx_renderer.bpm_modifier,
                    BlueprintFileDescriptor::max_len_in_beats(
                        &mut db_handler,
                        blueprint_to_instruction.id,
                    ),
                )
            };

            if blueprint_to_instruction.delay {
                tokio::time::sleep(beatduration.as_duration(bpm, bpm_modifier))
                    .await;
            }
        }
        Instruction::TimecodeTo(timecode) => {
            let mut db_handler = db_handler.lock().await;
            let mut dmx_renderer = dmx_renderer.lock().await;

            timecode_to(
                timecode.id,
                timecode.index,
                timecode.play,
                &mut dmx_renderer,
                &mut db_handler,
                selected_fixtures,
            );
        }
        Instruction::UnimplementedChannelTo(unimplemented_channel_setter) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            let unimplemented_channel_setter_value =
                unimplemented_channel_setter.value.clone();

            let fade_duration_beat_count = unimplemented_channel_setter
                .fade_duration_beat_count
                .clone()
                .and_then(|fade_duration_instruction| {
                    fade_duration_instruction.to_num(
                        value,
                        &dmx_renderer,
                        &input_parser,
                        scope,
                    )
                });

            if let Some(unimplemented_channel_setter_value) =
                unimplemented_channel_setter_value
            {
                let value = unimplemented_channel_setter_value.to_num(
                    value,
                    &dmx_renderer,
                    &input_parser,
                    scope,
                );
                dmx_renderer.unimplemented_channel_to(
                    selected_fixtures,
                    &unimplemented_channel_setter.name,
                    value,
                    fade_duration_beat_count,
                );
            }
        }
        Instruction::AddToPan(modifier) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(modifier) = modifier {
                let modifier = modifier
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(0);
                dmx_renderer.add_to_pan(selected_fixtures, modifier);
            }
        }
        Instruction::PanTo(pan) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(pan) = pan {
                if let Some(pan) =
                    pan.to_num(value, &dmx_renderer, &input_parser, scope)
                {
                    dmx_renderer.pan_to(selected_fixtures, pan);
                }
            }
        }
        Instruction::AddToTilt(modifier) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(modifier) = modifier {
                let modifier = modifier
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(0);
                dmx_renderer.add_to_tilt(selected_fixtures, modifier);
            }
        }
        Instruction::TiltTo(tilt) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(tilt) = tilt {
                if let Some(tilt) =
                    tilt.to_num(value, &dmx_renderer, &input_parser, scope)
                {
                    dmx_renderer.tilt_to(selected_fixtures, tilt);
                }
            }
        }
        Instruction::DimmerTo(master) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(master) = master {
                let master = master
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(0);
                dmx_renderer.set_dimmer_value(master, selected_fixtures);
            }
        }
        Instruction::PositionTo(position_id) => {
            let db_handler = db_handler.lock().await;
            let mut dmx_renderer = dmx_renderer.lock().await;

            position_to(
                *position_id,
                &db_handler,
                &mut dmx_renderer,
                selected_fixtures,
            );
        }
        Instruction::BpmTo(bpm) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(bpm) = bpm {
                let bpm_value = bpm
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(DEFAULT_BPM);
                #[allow(
                    clippy::cast_possible_truncation,
                    clippy::as_conversions
                )]
                dmx_renderer.bpm_to(bpm_value);
            }
        }
        Instruction::BpmModifierTo(bpm_modifier) => {
            let mut dmx_renderer = dmx_renderer.lock().await;
            let input_parser = input_parser.lock().await;

            if let Some(bpm_modifier) = bpm_modifier {
                if let Some(value) = bpm_modifier.to_num(
                    value,
                    &dmx_renderer,
                    &input_parser,
                    scope,
                ) {
                    if value == 100 {
                        dmx_renderer.bpm_modifier_to(DEFAULT_BPM_MODIFIER);
                    } else {
                        dmx_renderer.bpm_modifier_to(
                            #[allow(
                                clippy::cast_precision_loss,
                                clippy::as_conversions,
                                clippy::cast_possible_truncation,
                                clippy::cast_sign_loss
                            )]
                            (value.clamp(0, 200) as f32)
                                .map_range((0., 200.), (0., 2.))
                                .unwrap_or(DEFAULT_BPM_MODIFIER),
                        );
                    }
                } else {
                    dmx_renderer.bpm_modifier_to(DEFAULT_BPM_MODIFIER);
                }
            }
        }
        Instruction::CreateVariable(name) => {
            input_parser
                .lock()
                .await
                .create_variable(name.clone(), scope);
        }
        Instruction::SetVariable(variable_setter) => {
            if let Some(ref var_value) = variable_setter.value {
                let dmx_renderer = dmx_renderer.lock().await;
                let mut input_parser = input_parser.lock().await;

                let other_value = var_value
                    .to_num(value, &dmx_renderer, &input_parser, scope)
                    .unwrap_or(0);

                drop(dmx_renderer);

                input_parser.set_variable_value(
                    &variable_setter.name,
                    other_value,
                    scope,
                );
            }
        }
        Instruction::DelayByNotes(delay_instruction) => {
            let (bpm, bpm_modifier, beatduration) = {
                let dmx_renderer = dmx_renderer.lock().await;
                let input_parser = input_parser.lock().await;

                let count = delay_instruction
                    .count
                    .as_ref()
                    .and_then(|c| {
                        c.to_num(value, &dmx_renderer, &input_parser, scope)
                    })
                    .unwrap_or(1);

                let delay =
                    BeatDuration::new(delay_instruction.note_type, count);
                (dmx_renderer.bpm(), dmx_renderer.bpm_modifier, delay)
            };

            tokio::time::sleep(beatduration.as_duration(bpm, bpm_modifier))
                .await;
        }
        Instruction::IfStatement(if_statement) => {
            for if_statement in if_statement {
                let execute_childs = {
                    let dmx_renderer = dmx_renderer.lock().await;
                    let input_parser = input_parser.lock().await;

                    if_statement.comparison.is_true(
                        value,
                        &dmx_renderer,
                        &input_parser,
                        scope,
                    )
                };

                if execute_childs {
                    Box::pin(evaluate_instructions(
                        input_parser,
                        dmx_renderer,
                        value,
                        &if_statement.content,
                        db_handler,
                        scope,
                        selected_fixtures,
                    ))
                    .await;

                    break;
                }
            }
        }
        #[allow(clippy::option_if_let_else)]
        Instruction::LoopStatement(e) => {
            let mut interval = tokio::time::interval(
                core::time::Duration::from_millis(DMX_FRAME_RATE_IN_MS),
            );
            loop {
                interval.tick().await;

                let execute_childs = {
                    let dmx_renderer = dmx_renderer.lock().await;
                    let input_parser = input_parser.lock().await;

                    if let Some(ref left_value) = e.comparison.left_value {
                        let left_value = left_value
                            .to_num(value, &dmx_renderer, &input_parser, scope)
                            .unwrap_or(0);

                        if let Some(ref right_value) = e.comparison.right_value
                        {
                            let right_value = right_value
                                .to_num(
                                    value,
                                    &dmx_renderer,
                                    &input_parser,
                                    scope,
                                )
                                .unwrap_or(0);

                            drop(dmx_renderer);

                            match e.comparison.comparator {
                                Comparator::Less => left_value < right_value,
                                Comparator::LessOrEqual => {
                                    left_value <= right_value
                                }
                                Comparator::Greater => left_value > right_value,
                                Comparator::GreaterOrEqual => {
                                    left_value >= right_value
                                }
                                Comparator::Equal => left_value == right_value,
                                Comparator::NotEqual => {
                                    left_value != right_value
                                }
                            }
                        } else {
                            false
                        }
                    } else {
                        false
                    }
                };
                if !execute_childs {
                    break;
                }
                Box::pin(evaluate_instructions(
                    input_parser,
                    dmx_renderer,
                    value,
                    &e.content,
                    db_handler,
                    scope,
                    selected_fixtures,
                ))
                .await;
            }
        }
        Instruction::IfQueueAllowsContinue(childs) => {
            let mut input_parser = input_parser.lock().await;

            for child in childs {
                input_parser.instruction_queue.push(QueuedInstruction {
                    instruction: child.clone(),
                    value,
                    scope,
                });
            }
        }
        Instruction::ClearBlueprint(blueprint_id) => {
            dmx_renderer.lock().await.clear_splines_by_blueprint_id(
                selected_fixtures,
                *blueprint_id,
            );
        }
        Instruction::StartRecording => {
            input_parser.lock().await.loopstation.start_recording();
        }
        Instruction::StopRecording => {
            input_parser.lock().await.loopstation.stop_recording();
        }
        Instruction::ClearRecording => {
            input_parser.lock().await.loopstation.clear_recording();
        }
        Instruction::Print(print_statement) => {
            let dmx_renderer = dmx_renderer.lock().await;
            let mut input_parser = input_parser.lock().await;

            let text_part = &print_statement.text;
            let value_part = print_statement.value.clone().map_or(
                String::new(),
                |value_instruction| {
                    value_instruction
                        .to_num(value, &dmx_renderer, &input_parser, scope)
                        .map_or_else(
                            || "N/A".to_string(),
                            |num| num.to_string(),
                        )
                },
            );
            drop(dmx_renderer);
            let message = format!(
                "{text_part}{}{value_part}",
                if value_part.is_empty() { "" } else { ": " }
            );
            input_parser.append_print(message.clone());
            drop(input_parser);
            logging::log(message, logging::LogLevel::Info, false);
        }
        Instruction::Nop => (),
    }
}

fn position_to(
    position_id: usize,
    db_handler: &DbHandler,
    dmx_renderer: &mut DmxRenderer,
    selected_fixtures: &[ActivatedFixture],
) {
    if let Some(position) = db_handler
        .active_show
        .positions
        .iter()
        .find(|iter_position| iter_position.id == position_id)
    {
        dmx_renderer.set_pan_tilt_position(selected_fixtures, position);
    }
}
fn color_to(
    value: usize,
    input_parser: &InputParser,
    dmx_renderer: &mut DmxRenderer,
    color_to_instruction: &ColorToInstruction,
    scope: Option<usize>,
    selected_fixtures: &[ActivatedFixture],
) {
    dmx_renderer.set_color(
        selected_fixtures,
        color_to_instruction.color,
        color_to_instruction
            .fade_duration_beat_count
            .clone()
            .and_then(|fade_duration_instruction| {
                fade_duration_instruction.to_num(
                    value,
                    dmx_renderer,
                    input_parser,
                    scope,
                )
            }),
    );
}

fn color_to_random(
    value: usize,
    input_parser: &InputParser,
    dmx_renderer: &mut DmxRenderer,
    fade_duration: Option<InstructionValue>,
    scope: Option<usize>,
    selected_fixtures: &[ActivatedFixture],
) {
    dmx_renderer.set_color(
        selected_fixtures,
        RgbColor::new_random(),
        fade_duration.map(|fade_duration_instruction| {
            fade_duration_instruction
                .to_num(value, dmx_renderer, input_parser, scope)
                .unwrap_or_default()
        }),
    );
}
fn blueprint_to(
    id: usize,
    oneshot: bool,
    dmx_renderer: &mut DmxRenderer,
    db_handler: &DbHandler,
    selected_fixtures: &[ActivatedFixture],
) {
    for blueprint in &db_handler.active_show.blueprints {
        if blueprint.id == id {
            dmx_renderer.apply_blueprint(
                selected_fixtures,
                oneshot,
                blueprint,
                &db_handler.active_show.positions,
            );
        }
    }
}
fn timecode_to(
    id: usize,
    index: usize,
    timecode_advancing: bool,
    dmx_renderer: &mut DmxRenderer,
    db_handler: &mut DbHandler,
    selected_fixtures: &[ActivatedFixture],
) {
    let timecode_to_apply = &db_handler
        .active_show
        .timecodes
        .iter()
        .find(|timecode| timecode.id == id)
        .cloned();

    if let Some(timecode) = timecode_to_apply {
        dmx_renderer.apply_timecode(
            selected_fixtures,
            timecode,
            index,
            timecode_advancing,
            db_handler,
        );
    }
}
fn activate_all_fixtures(
    db_handler: &DbHandler,
    selected_fixtures: &mut Vec<ActivatedFixture>,
) {
    for dmx_fixture in &db_handler.active_show.fixtures {
        if let Some(id) = dmx_fixture.id {
            selected_fixtures.push(ActivatedFixture {
                id,
                name: dmx_fixture.name.clone(),
                fixturetype: dmx_fixture.fixturetype.clone(),
            });
        }
    }
}
fn activate_fixture_by_id(
    db_handler: &DbHandler,
    fx_id: usize,
    selected_fixtures: &mut Vec<ActivatedFixture>,
) {
    for dmx_fixture in &db_handler.active_show.fixtures {
        if let Some(dmx_fixture_id) = dmx_fixture.id {
            if dmx_fixture_id == fx_id {
                if let Some(id) = dmx_fixture.id {
                    selected_fixtures.push(ActivatedFixture {
                        id,
                        name: dmx_fixture.name.clone(),
                        fixturetype: dmx_fixture.fixturetype.clone(),
                    });
                }
            }
        }
    }
}
fn activate_fixture_group(
    db_handler: &DbHandler,
    needle_group_name: &str,
    selected_fixtures: &mut Vec<ActivatedFixture>,
) {
    let Some(group) = db_handler
        .active_show
        .enriched_groups()
        .into_iter()
        .find(|group| group.name.as_str() == needle_group_name)
    else {
        logging::log(
            format!("No group `{needle_group_name}` found"),
            logging::LogLevel::Warning,
            false,
        );
        return;
    };
    let appendable_fixtures: Vec<usize> = group
        .fixture_ids
        .iter()
        .filter(|group_fixture_id| {
            db_handler
                .active_show
                .fixtures
                .iter()
                .map(|show_fixture| show_fixture.id)
                .any(|show_fixture_id| {
                    show_fixture_id == Some(**group_fixture_id)
                })
        })
        .copied()
        .collect();

    let mut mapped_appendable_fixtures = appendable_fixtures
        .iter()
        .map(|fixture_id| {
            db_handler
                .active_show
                .fixtures
                .iter()
                .find(|show_fixture_id| show_fixture_id.id == Some(*fixture_id))
        })
        .filter(Option::is_some)
        .map(|show_fixture| {
            show_fixture.map_or_else(
                || {
                    logging::log(
                        format!("show-fixture was faulty while activating from group"),
                        logging::LogLevel::Warning,
                        false,
                    );
                    ActivatedFixture {
                        id: 0,
                        name: String::from("-"),
                        fixturetype: String::from("-"),
                    }
                },
                |show_fixture| {
                    show_fixture.id.map_or_else(|| {
                            logging::log(
                                format!("show-fixture was faulty while activating from group"),
                                logging::LogLevel::Warning,
                                false,
                            );
                            ActivatedFixture {
                                id: 0,
                                name: String::from("-"),
                                fixturetype: String::from("-"),
                            }
                        },
                        |id| {
                            ActivatedFixture {
                                id,
                                name: show_fixture.name.clone(),
                                fixturetype: show_fixture.fixturetype.clone(),
                            }
                        }
                    )}
            )
        })
        .collect();

    selected_fixtures.append(&mut mapped_appendable_fixtures);
}
fn set_blueprint_position_index_offset_mode(
    position_index_offset_mode: PositionIndexOffsetMode,
    dmx_renderer: &mut DmxRenderer,
    selected_fixtures: &[ActivatedFixture],
) {
    dmx_renderer.set_position_index_offset_mode(
        selected_fixtures,
        position_index_offset_mode,
    );
}
