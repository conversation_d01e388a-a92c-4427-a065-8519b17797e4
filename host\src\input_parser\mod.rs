/// Beatduration structure and utilities for musical time representation
pub mod beatduration;
/// Functions in this will start threads to collect the inputs from modules and rest.
pub mod collectors;
/// This is responsible for parsing the collected data and invoking functions according to that.
pub mod dmx_communicator_functions;
/// This holds functions and structs for `RWModules`
pub mod modules;
/// This holds structs for the collecting and parsing to store some state.
pub mod structs;
