use super::structs::InputParser;
use crate::logging;
use alloc::sync::Arc;
use core::time::Duration;
use rwm_package_definitions::{RwModule, RwModuleUsbPackage};
use serialport::{available_ports, SerialPort, SerialPortInfo};
use std::thread;
use std::time::Instant;
use tokio::sync::Mutex;

const USB_BAUD_RATE: u32 = 250_000;

const MAX_UNREACHABLE_MODULES: usize = 10;
// TODO: Can this be removed with the new protocoll?
const MAX_KNOWN_BUTTON_ID: u16 = 128;

#[allow(clippy::too_many_lines)]
pub fn spawn_usb_port_collector(input_parser: Arc<Mutex<InputParser>>) {
    tokio::spawn(async move {
        logging::log(
            format!("Started USB-module communicator"),
            logging::LogLevel::Info,
            false,
        );
        let mut modules: Vec<Box<dyn SerialPort>> = vec![];
        let mut send_keep_alive_timestamp = Instant::now();
        let mut reduce_unreachable_modules_count_timer = Instant::now();
        let mut unreachable_modules_count: usize = 0;
        let mut observed_usb_connection_count: usize = 0;
        #[allow(unused_assignments)]
        loop {
            let Ok(to_be_checked_ports) = available_ports() else {
                logging::log(
                    "Could not read available usb ports".to_owned(),
                    logging::LogLevel::Warning,
                    true,
                );
                thread::sleep(Duration::from_millis(100));
                continue;
            };
            if observed_usb_connection_count != to_be_checked_ports.len() {
                modules = vec![]; // We need to release all serialports
                observed_usb_connection_count = to_be_checked_ports.len();
                modules =
                    gather_connected_serial_input_modules(&to_be_checked_ports);
            }
            if modules.is_empty() {
                thread::sleep(Duration::from_millis(100));
                continue;
            }
            if send_keep_alive_timestamp.elapsed() > Duration::from_secs(4) {
                let mut reachable_modules: usize = 0;
                for port in &mut modules {
                    let send_buffer = [122, 127, 115, 6];
                    let mut recv_buffer = [0_u8; 32];
                    let _ = port.write(&send_buffer);
                    thread::sleep(Duration::from_millis(10));
                    let length_read = port.read(&mut recv_buffer);
                    if let Ok(length_read) = length_read {
                        let rw_module_usb_package: RwModuleUsbPackage =
                            (length_read, recv_buffer.as_slice()).into();
                        if RwModuleUsbPackage::ImAlive == rw_module_usb_package
                        {
                            reachable_modules =
                                reachable_modules.saturating_add(1);
                        }
                    }
                }
                if reachable_modules != modules.len() || reachable_modules == 0
                {
                    unreachable_modules_count =
                        unreachable_modules_count.saturating_add(1);
                    logging::log(
                        format!("Found unreachable module {unreachable_modules_count}/{MAX_UNREACHABLE_MODULES}"),
                        logging::LogLevel::Warning,
                        true,
                        );
                }
                send_keep_alive_timestamp = Instant::now();
            }
            if unreachable_modules_count == MAX_UNREACHABLE_MODULES {
                unreachable_modules_count = 0;
                modules = vec![]; // We need to release all serialports
                logging::log(
                    format!(
                        "Restarted USB-module communicator (module unreachable)"
                    ),
                    logging::LogLevel::Info,
                    true,
                );
                modules =
                    gather_connected_serial_input_modules(&to_be_checked_ports);
                continue;
            }
            if reduce_unreachable_modules_count_timer.elapsed()
                > Duration::from_secs(16)
            {
                unreachable_modules_count =
                    unreachable_modules_count.saturating_sub(1);
                reduce_unreachable_modules_count_timer = Instant::now();
            }
            let Ok(packages) = gather_serial_input(&mut modules) else {
                modules = vec![]; // We need to release all serialports
                logging::log(
                    format!(
                        "Restarted USB-module communicator (module panicked)"
                    ),
                    logging::LogLevel::Info,
                    true,
                );
                modules =
                    gather_connected_serial_input_modules(&to_be_checked_ports);
                continue;
            };
            for rw_module_usb_package in packages {
                if let RwModuleUsbPackage::Input(e) = rw_module_usb_package {
                    input_parser.lock().await.push_to_inputs(e.into());
                }
            }
            tokio::time::sleep(Duration::from_millis(1)).await;
        }
    });
}

// TODO: this would also benefit from async
fn gather_connected_serial_input_modules(
    to_be_checked_ports: &[SerialPortInfo],
) -> Vec<Box<dyn SerialPort>> {
    logging::log(
        "(re)connecting rw-modules".to_owned(),
        logging::LogLevel::Info,
        false,
    );
    let mut mapped_inputs = vec![];
    let mut port_checking_handles = vec![];
    for port in to_be_checked_ports {
        let port = port.clone();
        port_checking_handles.push(thread::spawn(move || {
            let Ok(mut port) =
                serialport::new(format!("{}", port.port_name), USB_BAUD_RATE)
                    .timeout(Duration::from_millis(20))
                    .open()
            else {
                return None;
            };
            let mut garbage = vec![];
            let _garbage_can = port.read_to_end(&mut garbage);

            let send_buffer = [122, 127, 115, 1];
            let mut recv_buffer = [0_u8; 32];
            // The RWM needs time to init after connecting to boot
            thread::sleep(Duration::from_millis(2000));
            let _ = port.write(&send_buffer);
            thread::sleep(Duration::from_millis(10));
            let length_read = port.read(&mut recv_buffer);
            if let Ok(length_read) = length_read {
                let rw_module_usb_package: RwModuleUsbPackage =
                    (length_read, recv_buffer.as_slice()).into();
                if let RwModuleUsbPackage::ModuleIdentifier(m) =
                    rw_module_usb_package
                {
                    logging::log(
                        format!("Found module {m:?}"),
                        logging::LogLevel::Info,
                        false,
                    );
                    return Some(port);
                }
            }

            None
        }));
    }
    for result in port_checking_handles {
        if let Ok(Some(result)) = result.join() {
            mapped_inputs.push(result);
        }
    }
    mapped_inputs
}
fn gather_serial_input(
    modules: &mut Vec<Box<dyn SerialPort>>,
) -> Result<Vec<RwModuleUsbPackage>, RwModule> {
    let mut result = vec![];
    let mut buffer: [u8; 32];
    for port in modules {
        buffer = [0; 32];
        match port.read(&mut buffer) {
            Ok(0) => {
                logging::log(
                    String::from("Input source may be unplugged!"),
                    logging::LogLevel::Warning,
                    false,
                );
            }
            Ok(n) => {
                let rw_module_usb_package: RwModuleUsbPackage =
                    (n, buffer.as_slice()).into();
                if RwModuleUsbPackage::None == rw_module_usb_package {
                    continue;
                }
                if let RwModuleUsbPackage::Input(input) =
                    rw_module_usb_package.clone()
                {
                    if input.id > MAX_KNOWN_BUTTON_ID {
                        continue;
                    }
                }
                if let RwModuleUsbPackage::Panic(m) =
                    rw_module_usb_package.clone()
                {
                    logging::log(
                        format!("Module {m:?} has paniced"),
                        logging::LogLevel::Warning,
                        true,
                    );
                    return Err(m);
                }

                let send_buffer = [122, 127, 115, 5];
                let _ = port.write(&send_buffer);
                result.push(rw_module_usb_package);
            }
            Err(_) => (),
        }
    }
    Ok(result)
}
