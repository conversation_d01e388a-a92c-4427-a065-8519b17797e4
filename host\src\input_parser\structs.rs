use crate::database_handler::snippet::Snippet;
use crate::database_handler::snippet::SnippetCategory;
use crate::database_handler::DbHandler;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::channel::unimplemented_channels::UnimplementedChannelSetter;
use crate::dmx_renderer::fixture::PositionIndexOffsetMode;
use crate::dmx_renderer::DmxRenderer;
use crate::dmx_renderer::DEFAULT_BPM;
use crate::input_parser::beatduration::BeatDuration;
use crate::input_parser::beatduration::NoteType;
use crate::input_parser::collectors::spawn_beat_receive_thread;
use crate::logging;
use core::fmt;
use core::time::Duration;
use rand::{thread_rng, Rng};
use std::time::Instant;
use tokio::task::Jo<PERSON><PERSON>andle;
use ts_rs::TS;

use super::dmx_communicator_functions::{PRESSED, RELEASED};

use alloc::sync::Arc;
use tokio::sync::Mutex;

use serde::Deserialize;
use serde::Serialize;

use super::dmx_communicator_functions::evaluate_instructions;

pub const MAX_INSTRUCTION_EVALUATION_THREADS: usize = 64;

#[derive(Clone, Debug, PartialEq, Eq, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct RawInput {
    pub id: u16,
    pub value: usize,
}

impl From<rwm_package_definitions::RawInput> for RawInput {
    fn from(value: rwm_package_definitions::RawInput) -> Self {
        Self {
            id: value.id,
            value: value.value.into(),
        }
    }
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct FunctionGetter {
    pub fixture_id: usize,
    pub function: String,
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct MathOperator {
    pub left_value: Option<InstructionValue>,
    pub right_value: Option<InstructionValue>,
    pub operand: Operand,
}

#[derive(Clone, Copy, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub enum Operand {
    Addition,
    Subtraction,
    Multiplication,
    Division,
    Modulus,
}
impl From<String> for Operand {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Subtraction" => Self::Subtraction,
            "Multiplication" => Self::Multiplication,
            "Division" => Self::Division,
            "Modulus" => Self::Modulus,
            _ /* | "Addition" */ => Self::Addition,
        }
    }
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum InstructionValue {
    Number(usize),
    Variable(String),
    Function(FunctionGetter),
    Keypoint(usize),
    BlueprintSpeedOfFixture(usize),
    BlueprintIntensityOfFixture(usize),
    Value,
    Pressed,
    Released,
    Default,
    Bpm,
    BpmModifier,
    ProgramStartTimestamp,
    MathOperator(Box<MathOperator>),
    Random((Box<Option<InstructionValue>>, Box<Option<InstructionValue>>)),
}

impl InstructionValue {
    #[must_use]
    #[allow(clippy::too_many_lines)]
    pub fn to_num(
        &self,
        value: usize,
        dmx_renderer: &DmxRenderer,
        input_parser: &InputParser,
        scope: Option<usize>,
    ) -> Option<usize> {
        Some(match self {
            Self::Number(number_value) => *number_value,
            Self::Value => value,
            Self::Pressed => PRESSED,
            Self::Released => RELEASED,
            Self::Default => return None,
            Self::Random(range) => {
                let range_0 = *range.0.clone();
                let range_1 = *range.1.clone();
                let range: (usize, usize) = (
                    range_0?
                        .to_num(value, dmx_renderer, input_parser, scope)
                        .unwrap_or(usize::MIN),
                    range_1?
                        .to_num(value, dmx_renderer, input_parser, scope)
                        .unwrap_or(usize::MAX),
                );
                if range.0 < range.1 {
                    let mut rng = thread_rng();
                    rng.gen_range(range.0..range.1)
                } else {
                    range.0
                }
            }
            Self::Keypoint(keypoint_id) => {
                let keypoints = dmx_renderer.all_keypoints();
                for keypoint in &keypoints {
                    if keypoint.id == Some(*keypoint_id) {
                        return Some(keypoint.value.into());
                    }
                }
                usize::MIN
            }

            Self::Bpm => dmx_renderer.bpm(),
            #[allow(
                clippy::cast_possible_truncation,
                clippy::as_conversions,
                clippy::cast_sign_loss,
                clippy::cast_precision_loss
            )]
            Self::BpmModifier => {
                dmx_renderer.bpm_modifier().clamp(0., usize::MAX as f32)
                    as usize
            }
            Self::Function(function_getter) => usize::from(
                dmx_renderer.get_function_value_of_fixture(function_getter)?,
            ),
            Self::BlueprintSpeedOfFixture(fixture_id) => {
                let speed =
                    dmx_renderer.get_blueprint_speed_of_fixture(*fixture_id)?;
                #[allow(
                    clippy::cast_possible_truncation,
                    clippy::as_conversions
                )]
                let clamped_speed = speed.min(u8::MAX.into());
                clamped_speed
            }
            Self::ProgramStartTimestamp => input_parser
                .program_start_time
                .elapsed()
                .as_millis()
                .try_into()
                .unwrap_or(usize::MAX),
            Self::BlueprintIntensityOfFixture(fixture_id) => {
                dmx_renderer.get_blueprint_intensity_of_fixture(*fixture_id)?
            }
            Self::Variable(var_name) => {
                input_parser.get_variable(var_name, scope)?.value
            }
            Self::MathOperator(operator) => {
                let operator = *operator.clone();

                match operator.operand {
                    Operand::Addition => operator
                        .left_value?
                        .to_num(value, dmx_renderer, input_parser, scope)?
                        .saturating_add(operator.right_value?.to_num(
                            value,
                            dmx_renderer,
                            input_parser,
                            scope,
                        )?),
                    Operand::Subtraction => operator
                        .left_value?
                        .to_num(value, dmx_renderer, input_parser, scope)?
                        .saturating_sub(operator.right_value?.to_num(
                            value,
                            dmx_renderer,
                            input_parser,
                            scope,
                        )?),
                    Operand::Multiplication => operator
                        .left_value?
                        .to_num(value, dmx_renderer, input_parser, scope)?
                        .saturating_mul(operator.right_value?.to_num(
                            value,
                            dmx_renderer,
                            input_parser,
                            scope,
                        )?),
                    Operand::Division => operator
                        .left_value?
                        .to_num(value, dmx_renderer, input_parser, scope)?
                        .checked_div(operator.right_value?.to_num(
                            value,
                            dmx_renderer,
                            input_parser,
                            scope,
                        )?)?,
                    Operand::Modulus => operator
                        .left_value?
                        .to_num(value, dmx_renderer, input_parser, scope)?
                        .checked_rem(operator.right_value?.to_num(
                            value,
                            dmx_renderer,
                            input_parser,
                            scope,
                        )?)?,
                }
            }
        })
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
#[serde(tag = "instruction", content = "instructionMod")]
pub enum Instruction {
    // action
    ExecuteCallableSnippet(usize),
    ColorTo(ColorToInstruction),
    ColorToRandom(Option<InstructionValue>),
    ActivateAllFixtures,
    ActivateFixtureById(usize),
    ActivateFixtureGroup(String),
    BlueprintTo(BlueprintToInstruction),
    TimecodeTo(TimecodeToInstruction),
    SetBlueprintPositionIndexOffsetMode(PositionIndexOffsetMode),
    SetSpeedOfBlueprints(Option<InstructionValue>),
    SetBlueprintIntensity(Option<InstructionValue>),
    ToggleQueueMode(QueueMode),
    UnimplementedChannelTo(UnimplementedChannelSetter),
    PanTo(Option<InstructionValue>),
    AddToPan(Option<InstructionValue>),
    TiltTo(Option<InstructionValue>),
    AddToTilt(Option<InstructionValue>),
    PositionTo(usize),
    BpmTo(Option<InstructionValue>),
    BpmModifierTo(Option<InstructionValue>),
    FixtureLoop(FixtureLoop),
    CreateVariable(String),
    SetVariable(VariableSetter),
    AddToGroup(AddToGroup),
    RemoveFromGroup(RemoveFromGroup),
    DelayByNotes(DelayByNotesInstruction),
    DeselectAllFixtures,
    DimmerTo(Option<InstructionValue>),
    ClearBlueprint(usize),
    StartRecording,
    StopRecording,
    ClearRecording,
    Print(PrintStatement),

    // controllflow
    IfStatement(Vec<ConditionalBlock>),
    // TODO: this should be `LoopStatement(ConditionalBlock)`
    LoopStatement(LoopStatement),
    IfQueueAllowsContinue(Vec<Instruction>),

    // other
    Nop,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct TimecodeToInstruction {
    pub id: usize,
    pub index: usize,
    pub play: bool,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct BlueprintToInstruction {
    pub id: usize,
    pub oneshot: bool,
    pub delay: bool,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct ColorToInstruction {
    pub color: RgbColor,
    pub fade_duration_beat_count: Option<InstructionValue>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct PrintStatement {
    pub text: String,
    pub value: Option<InstructionValue>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct DelayByNotesInstruction {
    pub note_type: NoteType,
    pub count: Option<InstructionValue>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct RemoveFromGroup {
    pub name: String,
    pub fixtures: Vec<Instruction>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct AddToGroup {
    pub name: String,
    pub fixtures: Vec<Instruction>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct FixtureLoop {
    pub fixtures: Vec<Instruction>,
    pub instructions: Vec<Instruction>,
}

#[derive(Clone, Copy, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub enum Comparator {
    Less,
    LessOrEqual,
    Greater,
    GreaterOrEqual,
    Equal,
    NotEqual,
}
impl From<String> for Comparator {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Less" => Self::Less,
            "LessOrEqual" => Self::LessOrEqual,
            "Greater" => Self::Greater,
            "GreaterOrEqual" => Self::GreaterOrEqual,
            "NotEqual" => Self::NotEqual,
            _ /* | "Equal" */ => Self::Equal,
        }
    }
}
impl fmt::Display for Comparator {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Less => write!(f, "Less"),
            Self::LessOrEqual => write!(f, "LessOrEqual"),
            Self::Greater => write!(f, "Greater"),
            Self::GreaterOrEqual => write!(f, "GreaterOrEqual"),
            Self::Equal => write!(f, "Equal"),
            Self::NotEqual => write!(f, "NotEqual"),
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct Comparison {
    pub left_value: Option<InstructionValue>,
    pub right_value: Option<InstructionValue>,
    pub comparator: Comparator,
}
impl Comparison {
    pub const fn negate_comparator(&mut self) {
        self.comparator = match self.comparator {
            Comparator::Less => Comparator::GreaterOrEqual,
            Comparator::LessOrEqual => Comparator::Greater,
            Comparator::Greater => Comparator::LessOrEqual,
            Comparator::GreaterOrEqual => Comparator::Less,
            Comparator::Equal => Comparator::NotEqual,
            Comparator::NotEqual => Comparator::Equal,
        }
    }
    #[must_use]
    pub fn is_true(
        &self,
        value: usize,
        dmx_renderer: &DmxRenderer,
        input_parser: &InputParser,
        scope: Option<usize>,
    ) -> bool {
        let Some(ref left_value) = self.left_value else {
            return false;
        };
        let Some(left_value) =
            left_value.to_num(value, dmx_renderer, input_parser, scope)
        else {
            return false;
        };

        let Some(ref right_value) = self.right_value else {
            return false;
        };
        let Some(right_value) =
            right_value.to_num(value, dmx_renderer, input_parser, scope)
        else {
            return false;
        };

        match self.comparator {
            Comparator::Less => left_value < right_value,
            Comparator::LessOrEqual => left_value <= right_value,
            Comparator::Greater => left_value > right_value,
            Comparator::GreaterOrEqual => left_value >= right_value,
            Comparator::Equal => left_value == right_value,
            Comparator::NotEqual => left_value != right_value,
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct ConditionalBlock {
    pub comparison: Comparison,
    pub content: Vec<Instruction>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct LoopStatement {
    pub comparison: Comparison,
    pub content: Vec<Instruction>,
}

#[derive(Copy, Debug, PartialEq, Eq, Clone, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum QueueMode {
    Flush,
    Queue,
}
impl core::ops::Not for QueueMode {
    type Output = Self;

    fn not(self) -> Self::Output {
        match self {
            Self::Flush => Self::Queue,
            Self::Queue => Self::Flush,
        }
    }
}
impl From<String> for QueueMode {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Queue" => Self::Queue,
            _ /* | "Flush" */ => Self::Flush,
        }
    }
}
impl fmt::Display for QueueMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Flush => write!(f, "Flush"),
            Self::Queue => write!(f, "Queue"),
        }
    }
}

#[derive(Clone, Debug)]
pub struct QueuedInstruction {
    pub instruction: Instruction,
    pub value: usize,
    pub scope: Option<usize>,
}

#[derive(Clone, Debug)]
pub struct DelayableInstructionBatch {
    pub instructions: Vec<Instruction>,
    pub delay: BeatDuration,
    pub value: usize,
    pub selected_fixtures: Vec<ActivatedFixture>,
    pub scope: Option<usize>,
}

impl DelayableInstructionBatch {
    #[must_use]
    pub const fn new(
        value: usize,
        delay: BeatDuration,
        selected_fixtures: Vec<ActivatedFixture>,
        scope: Option<usize>,
    ) -> Self {
        Self {
            instructions: vec![],
            delay,
            value,
            selected_fixtures,
            scope,
        }
    }

    #[must_use]
    pub const fn new_with_delay(
        value: usize,
        delay: BeatDuration,
        selected_fixtures: Vec<ActivatedFixture>,
        scope: Option<usize>,
    ) -> Self {
        Self {
            instructions: vec![],
            delay,
            value,
            selected_fixtures,
            scope,
        }
    }
    #[must_use]
    pub fn from_snippet(snippet: &Snippet, value: usize) -> Self {
        Self {
            instructions: snippet.instructions.clone(),
            delay: BeatDuration::default(),
            value,
            selected_fixtures: vec![],
            scope: if snippet.category == SnippetCategory::Startup {
                None
            } else {
                Some(snippet.id)
            },
        }
    }
    #[must_use]
    pub fn from_instruction(
        instruction: &Instruction,
        value: usize,
        scope: Option<usize>,
    ) -> Self {
        Self {
            instructions: vec![instruction.clone()],
            delay: BeatDuration::default(),
            value,
            selected_fixtures: vec![],
            scope,
        }
    }
    #[must_use]
    pub fn from_queued_instruction(
        queued_instruction: &QueuedInstruction,
    ) -> Self {
        Self {
            instructions: vec![queued_instruction.instruction.clone()],
            delay: BeatDuration::default(),
            value: queued_instruction.value,
            selected_fixtures: vec![],
            scope: queued_instruction.scope,
        }
    }
    #[must_use]
    pub fn from_loop_statement(
        loop_statement: &LoopWithTailInstructions,
    ) -> Self {
        let mut instructions = vec![];
        instructions.push(loop_statement.loop_instruction.clone());
        for tail_instruction in &loop_statement.tail_instructions {
            instructions.push(tail_instruction.clone());
        }
        Self {
            instructions,
            delay: BeatDuration::default(),
            value: loop_statement.value,
            selected_fixtures: loop_statement.selected_fixtures.clone(),
            scope: loop_statement.scope,
        }
    }
}

#[derive(Clone, Debug)]
pub struct LoopWithTailInstructions {
    pub loop_instruction: Instruction,
    pub tail_instructions: Vec<Instruction>,
    pub value: usize,
    pub selected_fixtures: Vec<ActivatedFixture>,
    pub scope: Option<usize>,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ActivatedFixture {
    pub id: usize,
    pub name: String,
    pub fixturetype: String,
}

pub struct InputParser {
    pub raw_inputs: Vec<RawInput>,
    pub most_recent_input_activated: Option<u16>,
    pub beat_index: Arc<Mutex<(u8, bool)>>,
    pub time_since_last_beat: Arc<Mutex<i32>>,
    pub last_calculated_bpm: Arc<Mutex<usize>>,
    pub instruction_queue: Vec<QueuedInstruction>,
    pub one_time_instructions: Vec<DelayableInstructionBatch>,
    pub queue_mode: QueueMode,
    pub scoped_variables: Vec<ScopedVariable>,
    pub loopstation: LoopStation,
    pub program_start_time: Instant,
    pub print_buffer: Vec<String>,
    pub execution_threads: Vec<JoinHandle<()>>,
}

#[derive(Default)]
pub struct LoopStation {
    recorded_keypresses: Vec<(Duration, RawInput)>,
    timeframe_start: Option<Instant>,
    yielded_already_in_this_pass: usize,
    timeframe_duration: Option<Duration>,
}

impl LoopStation {
    pub fn start_recording(&mut self) {
        if !self.is_recording() {
            self.timeframe_start = Some(Instant::now());
        }
    }
    pub fn stop_recording(&mut self) {
        if self.is_recording() {
            self.timeframe_duration = Some(
                self.timeframe_start.unwrap_or_else(Instant::now).elapsed(),
            );
        }
    }
    pub fn clear_recording(&mut self) {
        self.recorded_keypresses = vec![];
        self.timeframe_start = None;
        self.timeframe_duration = None;
    }
    #[must_use]
    pub fn next_due_keypresses(&mut self) -> Vec<RawInput> {
        if let Some(start) = self.timeframe_start {
            if let Some(duration) = self.timeframe_duration {
                #[allow(clippy::arithmetic_side_effects)]
                let relative_duration_since_start = Duration::from_millis(
                    start.elapsed().as_millis().try_into().unwrap_or(u64::MAX)
                        % duration.as_millis().try_into().unwrap_or(u64::MAX),
                );

                #[allow(clippy::needless_collect)]
                {
                    let could_be_yielded_keypresses: Vec<(Duration, RawInput)> =
                        self.recorded_keypresses
                            .iter()
                            .filter(|(timestamp, _)| {
                                *timestamp < relative_duration_since_start
                            })
                            .cloned()
                            .collect();

                    if could_be_yielded_keypresses.len()
                        < self.yielded_already_in_this_pass
                    {
                        self.yielded_already_in_this_pass = 0;
                    }
                }

                let result: Vec<RawInput> = self
                    .recorded_keypresses
                    .iter()
                    .skip(self.yielded_already_in_this_pass)
                    .filter(|(timestamp, _)| {
                        *timestamp < relative_duration_since_start
                    })
                    .map(|(_, keypress)| keypress)
                    .cloned()
                    .collect();

                self.yielded_already_in_this_pass = self
                    .yielded_already_in_this_pass
                    .saturating_add(result.len());
                return result;
            }
        }
        vec![]
    }
    pub fn append_keypress(&mut self, keypress: &RawInput) {
        if let Some(timeframe_start) = self.timeframe_start {
            self.recorded_keypresses
                .push((timeframe_start.elapsed(), keypress.clone()));
        }
    }
    #[must_use]
    pub const fn is_recording(&self) -> bool {
        self.timeframe_start.is_some() && self.timeframe_duration.is_none()
    }
}

#[derive(Clone, Debug)]
pub struct ScopedVariable {
    pub name: String,
    pub value: usize,
    pub scope: Option<usize>,
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct VariableSetter {
    pub name: String,
    pub value: Option<InstructionValue>,
}

impl InputParser {
    #[must_use]
    #[allow(clippy::mutex_integer)]
    pub fn new(program_start_time: Instant) -> Self {
        let beat_index = Arc::new(Mutex::new((1, true)));
        let last_calculated_bpm = Arc::new(Mutex::new(DEFAULT_BPM));
        let time_since_last_beat = Arc::new(Mutex::new(1));
        spawn_beat_receive_thread(
            Arc::clone(&beat_index),
            Arc::clone(&last_calculated_bpm),
            Arc::clone(&time_since_last_beat),
        );
        Self {
            raw_inputs: vec![],
            most_recent_input_activated: None,
            beat_index,
            time_since_last_beat,
            last_calculated_bpm,
            instruction_queue: vec![],
            one_time_instructions: vec![],
            queue_mode: QueueMode::Flush,
            scoped_variables: vec![],
            loopstation: LoopStation::default(),
            program_start_time,
            print_buffer: vec![],
            execution_threads: vec![],
        }
    }
    #[allow(clippy::too_many_lines, clippy::cognitive_complexity)]
    pub async fn process_input(
        input_parser: &Arc<Mutex<Self>>,
        db_handler: &Arc<Mutex<DbHandler>>,
        dmx_renderer: &Arc<Mutex<DmxRenderer>>,
        execute_once_per_frame_actions: bool,
    ) {
        let instructions: Vec<DelayableInstructionBatch> = {
            let mut db_handler = db_handler.lock().await;
            let mut dmx_renderer = dmx_renderer.lock().await;
            let mut input_parser = input_parser.lock().await;

            if let Ok(e) = input_parser.time_since_last_beat.try_lock() {
                if *e == 0 {
                    if let Ok(bpm) = input_parser.last_calculated_bpm.try_lock()
                    {
                        if let Ok(mut index) =
                            input_parser.beat_index.try_lock()
                        {
                            dmx_renderer.set_beat_fraction_duration(*bpm);
                            *index = (1, true);
                        }
                    }
                }
            }
            drop(dmx_renderer);

            let mut instructions: Vec<DelayableInstructionBatch> = vec![];
            if let Some(startup_snippets) =
                db_handler.startup_snippets_if_needed()
            {
                for thread in &mut input_parser.execution_threads {
                    thread.abort();
                }
                input_parser.execution_threads = vec![];
                input_parser.scoped_variables = vec![];
                for snippet in &startup_snippets {
                    if !snippet.do_not_use_instructions {
                        instructions.push(
                            DelayableInstructionBatch::from_snippet(snippet, 0),
                        );
                    }
                }
            }
            if execute_once_per_frame_actions {
                for snippet in &db_handler.watcher() {
                    if !snippet.do_not_use_instructions {
                        instructions.push(
                            DelayableInstructionBatch::from_snippet(snippet, 0),
                        );
                    }
                }
            }
            for instruction in &input_parser.one_time_instructions {
                instructions.push(instruction.clone());
            }
            input_parser.one_time_instructions = vec![];

            for raw_input in &input_parser.raw_inputs {
                let mut snippet =
                    db_handler.find_snippet_by_serial_module_key(raw_input.id);

                if let Some(snippet) = snippet.as_mut() {
                    if !snippet.do_not_use_instructions {
                        instructions.push(
                            DelayableInstructionBatch::from_snippet(
                                snippet,
                                raw_input.value,
                            ),
                        );
                    }
                } else {
                    logging::log(
                        format!(
                            "Received key {} but found no snippet for it",
                            raw_input.id
                        ),
                        logging::LogLevel::Info,
                        false,
                    );
                }
            }
            input_parser.raw_inputs = vec![];

            for raw_input in input_parser.loopstation.next_due_keypresses() {
                let mut snippet =
                    db_handler.find_snippet_by_serial_module_key(raw_input.id);

                if let Some(snippet) = snippet.as_mut() {
                    if !snippet.do_not_use_instructions {
                        instructions.push(
                            DelayableInstructionBatch::from_snippet(
                                snippet,
                                raw_input.value,
                            ),
                        );
                    }
                } else {
                    logging::log(
                    format!(
                        "Loopstation activated key {} but found no snippet for it",
                        raw_input.id
                    ),
                    logging::LogLevel::Info,
                    false,
                );
                }
            }
            drop(db_handler);

            if input_parser.queue_mode == QueueMode::Flush {
                for queued_instruction in input_parser.instruction_queue.clone()
                {
                    instructions.push(
                        DelayableInstructionBatch::from_queued_instruction(
                            &queued_instruction,
                        ),
                    );
                }
                input_parser.instruction_queue = vec![];
            }

            instructions
        };

        if !instructions.is_empty() {
            for instruction_batch in instructions {
                let input_parser_evaluator = Arc::clone(input_parser);
                let dmx_renderer_evaluator = Arc::clone(dmx_renderer);
                let db_handler_evaluator = Arc::clone(db_handler);

                let mut selected_fixtures = vec![];

                let thread_handle = tokio::spawn(async move {
                    evaluate_instructions(
                        &input_parser_evaluator,
                        &dmx_renderer_evaluator,
                        instruction_batch.value,
                        &instruction_batch.instructions,
                        &db_handler_evaluator,
                        instruction_batch.scope,
                        &mut selected_fixtures,
                    )
                    .await;
                });

                input_parser
                    .lock()
                    .await
                    .execution_threads
                    .push(thread_handle);
            }

            input_parser
                .lock()
                .await
                .execution_threads
                .retain(|thread| !thread.is_finished());

            let thread_count =
                input_parser.lock().await.execution_threads.len();

            if thread_count > MAX_INSTRUCTION_EVALUATION_THREADS {
                let disposable_threads: Vec<JoinHandle<()>> = input_parser
                    .lock()
                    .await
                    .execution_threads
                    .drain(
                        ..(thread_count.saturating_sub(
                            MAX_INSTRUCTION_EVALUATION_THREADS - 1,
                        )),
                    )
                    .collect();

                for thread in disposable_threads {
                    thread.abort();
                }
            }
        }
    }
    pub fn create_variable(&mut self, name: String, scope: Option<usize>) {
        if name.is_empty() {
            return;
        }

        if self
            .scoped_variables
            .iter()
            .find(|variable| {
                variable.name == *name
                    && (variable.scope == scope || variable.scope.is_none())
            })
            .is_none()
        {
            self.scoped_variables.push(ScopedVariable {
                name,
                value: 0,
                scope,
            });
        }
    }
    #[must_use]
    pub fn get_variable(
        &self,
        name: &String,
        scope: Option<usize>,
    ) -> Option<ScopedVariable> {
        self.scoped_variables
            .iter()
            .find(|variable| {
                variable.name == *name
                    && (variable.scope == scope || variable.scope.is_none())
            })
            .cloned()
    }
    pub fn get_variable_mut(
        &mut self,
        name: &String,
        scope: Option<usize>,
    ) -> Option<&mut ScopedVariable> {
        self.scoped_variables.iter_mut().find(|variable| {
            variable.name == *name
                && (variable.scope == scope || variable.scope.is_none())
        })
    }

    pub fn set_variable_value(
        &mut self,
        name: &String,
        value: usize,
        scope: Option<usize>,
    ) {
        if let Some(variable) =
            self.scoped_variables.iter_mut().find(|variable| {
                variable.name == *name
                    && (variable.scope == scope || variable.scope.is_none())
            })
        {
            variable.value = value;
        }
    }
    pub fn push_to_inputs(&mut self, raw_input: RawInput) {
        self.most_recent_input_activated = Some(raw_input.id);
        if self.loopstation.is_recording() {
            self.loopstation.append_keypress(&raw_input);
        }
        self.raw_inputs.push(raw_input);
    }
    pub fn push_one_time_instructions(
        &mut self,
        instructions: &Vec<Instruction>,
        value: usize,
        // TODO: accept scope
    ) {
        for instruction in instructions {
            self.one_time_instructions.push(
                DelayableInstructionBatch::from_instruction(
                    instruction,
                    value,
                    None,
                ),
            );
        }
    }
    pub fn reset_to_defaults(&mut self) {
        self.raw_inputs = vec![];
        self.most_recent_input_activated = None;
        self.instruction_queue = vec![];
        self.one_time_instructions = vec![];
        self.queue_mode = QueueMode::Flush;
        self.scoped_variables = vec![];
        self.print_buffer = vec![];
    }

    pub fn append_print(&mut self, message: String) {
        if self.print_buffer.len() >= 10 {
            self.print_buffer.remove(0);
        }
        self.print_buffer.push(message);
    }
}
