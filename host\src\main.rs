extern crate alloc;

use alloc::sync::Arc;
use core::time::Duration;
use database_handler::{<PERSON><PERSON><PERSON><PERSON><PERSON>, PendingPublishTo};
use input_parser::modules::spawn_usb_port_collector;
use rest_api::spawn_rest_api;
use std::time::Instant;
use tokio::sync::Mutex;

#[allow(
    clippy::useless_format,
    clippy::print_with_newline,
    clippy::expect_fun_call,
    clippy::search_is_some,
    clippy::single_match
)]
pub mod input_parser;
use input_parser::structs::InputParser;

#[allow(
    clippy::useless_format,
    clippy::new_without_default,
    clippy::print_with_newline,
    clippy::expect_fun_call,
    clippy::search_is_some
)]
pub mod dmx_renderer;
use dmx_renderer::{DmxRenderer, DMX_FRAME_RATE_IN_MS};

pub mod rest_api;

pub mod database_handler;

pub mod logging;
use logging::Dashboard;

pub mod audio_analysis;
use audio_analysis::{
    beat_detector::BeatDetector, level_analyzer::LevelA<PERSON>yzer, AudioProcessor,
};

use crate::input_parser::{
    beatduration::BeatDuration, structs::MAX_INSTRUCTION_EVALUATION_THREADS,
};

#[tokio::main]
#[allow(clippy::too_many_lines)]
async fn main() -> Result<(), Box<dyn core::error::Error + Send + Sync>> {
    let program_start_time = Instant::now();
    logging::mark_start(get_mode_string().as_str());

    let mut db_handler = DbHandler::new();
    let dmx_renderer = Arc::new(Mutex::new(DmxRenderer::new(&mut db_handler)));
    let db_handler = Arc::new(Mutex::new(db_handler));
    let input_parser =
        Arc::new(Mutex::new(InputParser::new(program_start_time)));
    let dashboard = Arc::new(Mutex::new(Dashboard::new(&get_mode_string())));

    let audioconfig = audio_analysis::AudioConfig::default();
    let mut audioprocessor = audio_analysis::AudioProcessor::new(audioconfig);
    audioprocessor
        .analyzers
        .push(Box::new(LevelAnalyzer::new()));
    audioprocessor.analyzers.push(Box::new(BeatDetector::new()));
    let audioprocessor = Arc::new(Mutex::new(audioprocessor));
    AudioProcessor::spawn_audio_analysis(Arc::clone(&audioprocessor));

    spawn_usb_port_collector(Arc::clone(&input_parser));
    spawn_rest_api(
        Arc::clone(&db_handler),
        Arc::clone(&dmx_renderer),
        Arc::clone(&input_parser),
        Arc::clone(&dashboard),
        program_start_time,
    );

    let db_handler_inputprocessing_clone = Arc::clone(&db_handler);
    let dmx_renderer_inputprocessing_clone = Arc::clone(&dmx_renderer);
    let input_parser_inputprocessing_clone = Arc::clone(&input_parser);

    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(100));

        loop {
            interval.tick().await;

            {
                InputParser::process_input(
                    &input_parser_inputprocessing_clone,
                    &db_handler_inputprocessing_clone,
                    &dmx_renderer_inputprocessing_clone,
                    true,
                )
                .await;
            }
        }
    });

    let db_handler_rendering_clone = Arc::clone(&db_handler);
    let dmx_renderer_rendering_clone = Arc::clone(&dmx_renderer);
    let input_parser_rendering_clone = Arc::clone(&input_parser);

    tokio::spawn(async move {
        let mut interval =
            tokio::time::interval(Duration::from_millis(DMX_FRAME_RATE_IN_MS));
        let mut index_timestamp = Instant::now();

        loop {
            interval.tick().await;

            let mut db_handler_locked = db_handler_rendering_clone.lock().await;
            let mut dmx_renderer_locked =
                dmx_renderer_rendering_clone.lock().await;
            let mut input_parser_locked =
                input_parser_rendering_clone.lock().await;

            let index_timestamp_snapshot = index_timestamp;
            index_timestamp = Instant::now();

            let beat_duration = BeatDuration::beat_duration(
                dmx_renderer_locked.bpm(),
                dmx_renderer_locked.bpm_modifier,
            );
            dmx_renderer_locked.render(
                &mut db_handler_locked,
                &mut input_parser_locked,
                (index_timestamp_snapshot.elapsed(), beat_duration),
            );
        }
    });

    let db_handler_dashboard_clone = Arc::clone(&db_handler);
    let dmx_renderer_dashboard_clone = Arc::clone(&dmx_renderer);
    let input_parser_dashboard_clone = Arc::clone(&input_parser);

    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(1000));

        loop {
            interval.tick().await;

            {
                let db_handler_locked = db_handler_dashboard_clone.lock().await;
                let dmx_renderer_locked =
                    dmx_renderer_dashboard_clone.lock().await;
                let input_parser_locked =
                    input_parser_dashboard_clone.lock().await;
                let mut dashboard_locked = dashboard.lock().await;

                dashboard_locked.update(
                    &db_handler_locked,
                    &dmx_renderer_locked,
                    &input_parser_locked,
                );
            }
        }
    });

    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(250));

        loop {
            interval.tick().await;

            {
                let mut db_handler_locked = db_handler.lock().await;
                let mut dmx_renderer_locked = dmx_renderer.lock().await;
                if db_handler_locked
                    .check_and_reset_if_new_state_was_received_for(
                        &PendingPublishTo::DmxRenderer,
                    )
                {
                    dmx_renderer_locked.merge_new_fixtures_with_state(
                        DbHandler::fixtures_for_active_show(
                            &mut db_handler_locked.db_connection(),
                        ),
                    );
                }
            }
        }
    });

    tokio::spawn(async move {
        let metrics = tokio::runtime::Handle::current().metrics();
        let mut interval = tokio::time::interval(Duration::from_millis(1000));
        loop {
            interval.tick().await;

            let num_alive_tasks = metrics.num_alive_tasks();
            if num_alive_tasks >= MAX_INSTRUCTION_EVALUATION_THREADS / 2 {
                logging::log(
                    format!("High tokio-taskcount detected: {num_alive_tasks}"),
                    logging::LogLevel::Info,
                    true,
                );
            }
            let queue_depth = metrics.global_queue_depth();
            if queue_depth >= 4 {
                logging::log(
                    format!("High tokio-await-queue detected: {queue_depth}"),
                    logging::LogLevel::Info,
                    true,
                );
            }
        }
    });

    let _ = tokio::signal::ctrl_c().await;
    std::process::exit(0);
}

#[cfg(debug_assertions)]
fn get_mode_string() -> String {
    String::from("DEBUG")
}
#[cfg(not(debug_assertions))]
fn get_mode_string() -> String {
    String::from("RELEASE")
}
