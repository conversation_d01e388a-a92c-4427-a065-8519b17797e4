use gdtf_parser::fixture_type::FixtureType;
use gdtf_parser::utils::units::name::Name;
use serde::{Deserialize, Serialize};
use ts_rs::TS;

use crate::dmx_renderer::channel::{
    color_channels::DmxFixtureColor,
    movement_channels::MovementChannels,
    unimplemented_channels::{Keypoint, UnimplementedChannel},
};

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct GdtfFixtureFileDescriptor {
    pub fixturetype: String,
    pub movement_channels: Option<MovementChannels>,
    pub footprint_size: usize,
    pub color: Option<DmxFixtureColor>,
    pub unimplemented_channels: Vec<UnimplementedChannel>,
}

pub enum GdtfParseError {
    DmxModeNotFound,
}

impl GdtfFixtureFileDescriptor {
    pub fn from_parsed_gdtf_file(
        gdtf_fixture: FixtureType,
        dmx_mode: &Name,
    ) -> Result<Self, GdtfParseError> {
        let Some(dmx_mode) = gdtf_fixture.dmx_modes.get(dmx_mode) else {
            return Err(GdtfParseError::DmxModeNotFound);
        };
        let dmx_channels = dmx_mode.dmx_channels.clone();

        let mut footprint_size = 0;
        let mut unimplemented_channels: Vec<UnimplementedChannel> = vec![];

        for channel in &dmx_channels {
            if let Some(ref offset) = channel.offset {
                for offset in &offset.0 {
                    let Ok(channel_offset) = usize::try_from(*offset) else {
                        continue;
                    };
                    let Some(logical_channel) =
                        channel.logical_channels.first()
                    else {
                        continue;
                    };
                    let Some(name) = logical_channel.attribute.0.first() else {
                        continue;
                    };

                    let mut keypoints: Vec<Keypoint> = vec![];
                    for (name, channel_function) in
                        &logical_channel.channel_functions
                    {
                        keypoints.push(Keypoint {
                            id: None,
                            name: name.0.clone(),
                            value: channel_function
                                .dmx_from
                                .initial_value
                                .try_into()
                                .unwrap_or(0),
                        });
                    }
                    unimplemented_channels.push(UnimplementedChannel {
                        id: None,
                        name: name.0.clone(),
                        dimmable: false,
                        inverted: false,
                        default: 0,
                        keypoints,
                        value: 0,
                        spline: None,
                        oneshot: false,
                        blueprint_id: None,
                        channel: channel_offset,
                    });
                    footprint_size = footprint_size.max(channel_offset);
                }
            }
        }
        Ok(Self {
            fixturetype: gdtf_fixture.short_name,
            movement_channels: None,
            footprint_size,
            color: None,
            unimplemented_channels,
        })
    }
}
