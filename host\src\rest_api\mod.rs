mod gdtf_fixture;
mod route_fns;

use alloc::sync::Arc;
use axum::extract::rejection::JsonRejection;
use axum::extract::DefaultBodyLimit;
use axum::http::{Method, StatusCode};
use axum::routing::{delete, get, patch, post, put};
use axum::{Json, Router};
use core::net::SocketAddr;
use route_fns::{
    delete_audiofile, delete_snippet, delete_snippets_dir, download_audiofile,
    get_available_audiofile_names, patch_snippet_category,
    patch_snippet_directory_parent_directory,
    patch_snippet_do_not_use_instructions, patch_snippet_instructions,
    patch_snippet_name, patch_snippet_parent_directory,
    patch_snippet_requires_user_action_reason, patch_snippet_serial_module_key,
    patch_snippets_dir_name, put_new_default_snippet,
    put_new_default_snippets_dir, sync_audiofiles,
};
use std::time::Instant;
use tokio::sync::Mutex;

use crate::database_handler::Db<PERSON>and<PERSON>;
use crate::dmx_renderer::DmxRenderer;
use crate::input_parser::structs::InputParser;
use crate::rest_api::route_fns::{patch_fixturegroup, post_fixturegroup};
use crate::{logging, logging::Dashboard};
use tower_http::cors::{Any, CorsLayer};

use self::route_fns::{
    delete_blueprint, delete_fixture, delete_fixturegroup, delete_position,
    delete_show, delete_timecode, gdtf_authenticate, gdtf_get_list,
    get_available_show_names_with_active_hint, get_blueprints, get_dashboard,
    get_dmx_universe, get_fixture_from_gdtf_share, get_fixturegroups,
    get_fixtures, get_positions, get_show, get_snippets_dir, get_timecodes,
    get_variable, handle_keyevent, here_i_am, most_recent_key_id,
    patch_blueprint, patch_fixture, patch_timecode, patch_variable, ping_pong,
    post_blueprint, post_fixture, post_show, post_timecode,
    process_one_time_instructions, rename_position, rename_show,
    request_snippet_state_reset, set_active_show, shutdown,
    trigger_pan_tilt_position_creation, trigger_pan_tilt_position_update,
    upload_audiofile, upload_show, upload_snippet,
};

#[derive(Clone)]
pub struct RouteState {
    db_handler: Arc<Mutex<DbHandler>>,
    dmx_renderer: Arc<Mutex<DmxRenderer>>,
    input_parser: Arc<Mutex<InputParser>>,
    dashboard: Arc<Mutex<Dashboard>>,
    program_start_time: Instant,
}

const API_PORT: u16 = 4000;
#[allow(clippy::too_many_lines)]
pub fn spawn_rest_api(
    db_handler: Arc<Mutex<DbHandler>>,
    dmx_renderer: Arc<Mutex<DmxRenderer>>,
    input_parser: Arc<Mutex<InputParser>>,
    dashboard: Arc<Mutex<Dashboard>>,
    program_start_time: Instant,
) {
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(vec![
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::PATCH,
            Method::DELETE,
        ])
        .allow_headers(Any);

    let app = Router::new()
        .route("/ping", get(ping_pong))
        .route("/rw_discover", get(here_i_am))
        .route("/shutdown", put(shutdown))
        .route("/dashboard", get(get_dashboard))
        .route("/dmxuniverse/:id", get(get_dmx_universe))
        .route("/audiofile", post(upload_audiofile))
        .route("/available_audiofiles", get(get_available_audiofile_names))
        .route("/audiofile/:id", delete(delete_audiofile))
        .route("/audiofile/:id", get(download_audiofile))
        .route("/audiofiles/sync", put(sync_audiofiles))
        .layer(DefaultBodyLimit::max(33_554_432))
        .route("/last_keyevent", get(most_recent_key_id))
        .route("/keyevent", get(handle_keyevent))
        .route("/one_time_instructions", put(process_one_time_instructions))
        .route("/fixtures", get(get_fixtures))
        .route("/fixture", post(post_fixture))
        .route("/fixture/:id", delete(delete_fixture))
        .route("/fixture", patch(patch_fixture))
        .route("/snippets_dir", get(get_snippets_dir))
        .route("/snippets_dir/:id", delete(delete_snippets_dir))
        .route("/snippets_dir/:id", put(put_new_default_snippets_dir))
        .route("/snippets_dir/:id/name", patch(patch_snippets_dir_name))
        .route(
            "/snippets_dir/:id/move",
            patch(patch_snippet_directory_parent_directory),
        )
        .route("/snippets/:id", delete(delete_snippet))
        .route("/snippets/:id", put(put_new_default_snippet))
        .route("/snippets/:id/name", patch(patch_snippet_name))
        .route(
            "/snippets/:id/instructions",
            patch(patch_snippet_instructions),
        )
        .route("/snippets/:id/category", patch(patch_snippet_category))
        .route(
            "/snippets/:id/do_not_use_instructions",
            patch(patch_snippet_do_not_use_instructions),
        )
        .route(
            "/snippets/:id/serial_module_key",
            patch(patch_snippet_serial_module_key),
        )
        .route(
            "/snippets/:id/requires_user_action_reason",
            patch(patch_snippet_requires_user_action_reason),
        )
        .route("/snippets/:id/move", patch(patch_snippet_parent_directory))
        .route(
            "/request_snippet_state_reset",
            put(request_snippet_state_reset),
        )
        .route("/snippets_dir/:id/upload_snippet", post(upload_snippet))
        .route("/shows", get(get_available_show_names_with_active_hint))
        .route("/show/:id", get(get_show))
        .route("/show/:id/rename", patch(rename_show))
        .route("/show/:id/set_active", patch(set_active_show))
        .route("/show", post(post_show))
        .route("/show", put(upload_show))
        .route("/show/:id", delete(delete_show))
        .route("/blueprints", get(get_blueprints))
        .route("/blueprint", post(post_blueprint))
        .route("/blueprint", patch(patch_blueprint))
        .route("/blueprint/:id", delete(delete_blueprint))
        .route("/timecodes", get(get_timecodes))
        .route("/timecode", post(post_timecode))
        .route("/timecode", patch(patch_timecode))
        .route("/timecode/:id", delete(delete_timecode))
        .route("/positions", get(get_positions))
        .route(
            "/createpantiltposition",
            post(trigger_pan_tilt_position_creation),
        )
        .route(
            "/updatepantiltposition/:id",
            patch(trigger_pan_tilt_position_update),
        )
        .route("/position", patch(rename_position))
        .route("/position", delete(delete_position))
        .route("/fixturegroups", get(get_fixturegroups))
        .route("/fixturegroup", post(post_fixturegroup))
        .route("/fixturegroup", patch(patch_fixturegroup))
        .route("/fixturegroup/:id", delete(delete_fixturegroup))
        .route("/variables/:name", get(get_variable))
        .route("/variables/:name", patch(patch_variable))
        .route("/gdtf-share/authenticate", post(gdtf_authenticate))
        .route("/gdtf-share/get-list", post(gdtf_get_list))
        .route(
            "/gdtf-share/fixture/:rid/mode/:mode",
            post(get_fixture_from_gdtf_share),
        )
        .with_state(RouteState {
            db_handler,
            dmx_renderer,
            input_parser,
            dashboard,
            program_start_time,
        })
        .layer(cors);

    tokio::spawn(async move {
        logging::log(
            format!(
                "Started rest api thread                    port {API_PORT}"
            ),
            logging::LogLevel::Info,
            false,
        );
        if let Ok(bound_server) =
            axum::Server::try_bind(&SocketAddr::from(([0, 0, 0, 0], API_PORT)))
        {
            if bound_server.serve(app.into_make_service()).await.is_err() {
                logging::log(
                    format!("Failed to start rest server on port {API_PORT}"),
                    logging::LogLevel::Warning,
                    true,
                );
            }
        } else {
            logging::log(
                format!("Failed to bind to port {API_PORT} for the rest api. (might be already in use)"),
                logging::LogLevel::Warning,
                true,
            );
        }
    });
}

fn deserialize_json_body<T>(
    body: Result<Json<T>, JsonRejection>,
    route: &(&str, Method),
) -> Result<T, (StatusCode, String)> {
    match body {
        Ok(Json(payload)) => Ok(payload),
        Err(err) => {
            logging::log(
                format!(
                    "Failed to deserialize JSON body on {} ({}). {err:?}",
                    route.0, route.1
                ),
                logging::LogLevel::Warning,
                true,
            );
            Err((
                StatusCode::UNPROCESSABLE_ENTITY,
                format!("Failed to deserialize JSON body. {err:?}"),
            ))
        }
    }
}
