use crate::database_handler::audiofiles::sync_audiofiles_with_filesystem;
use crate::database_handler::fixturegroups::FixtureGroup;
use crate::database_handler::show::Show;
use crate::dmx_renderer::dynamics::timecode::TimecodeFileDescriptor;
use crate::rest_api::gdtf_fixture::GdtfFixtureFileDescriptor;
use axum::extract::rejection::JsonRejection;
use axum::extract::{multipart::Multipart, State};
use axum::extract::{Path, Query};
use axum::http::{header, Method, StatusCode};
use axum::response::IntoResponse;
use axum::Json;
use core::time::Duration;
use serde::{Deserialize, Serialize};
use std::io::Cursor;
use std::process::Command;
use ts_rs::TS;

use crate::database_handler::directory_tree::Directory;
use crate::database_handler::pan_tilt_positions::ComposedPanTiltPosition;
use crate::database_handler::snippet::{Snippet, SnippetCategory};
use crate::database_handler::DbHandler;
use crate::dmx_renderer::dynamics::blueprint::BlueprintFileDescriptor;
use crate::dmx_renderer::fixture::DmxFixtureFileDescriptor;
use crate::input_parser::structs::{Instruction, RawInput};
use crate::logging;

use super::{deserialize_json_body, RouteState};

fn sanitize_filename(filename: &str) -> String {
    let sanitized = filename
        .chars()
        .map(|c| match c {
            c if c.is_alphanumeric() || c == '.' || c == '_' || c == ' ' => c,
            _ => '_',
        })
        .collect::<String>();

    let cleaned = sanitized
        .split(&['_', ' '][..])
        .filter(|s| !s.is_empty())
        .collect::<Vec<&str>>()
        .join("_");

    let result = cleaned.chars().take(255).collect::<String>();
    result
}

#[allow(clippy::unused_async)]
pub async fn ping_pong(
    State(_): State<RouteState>,
) -> (StatusCode, Json<String>) {
    (StatusCode::OK, Json(env!("CARGO_PKG_VERSION").to_owned()))
}

#[derive(Clone, serde::Serialize, TS)]
#[ts(export)]
pub struct BackendInfo {
    name: String,
    version: String,
    artnet_sending: bool,
    uptime_seconds: u64,
}

#[allow(clippy::unused_async)]
pub async fn here_i_am(
    State(state): State<RouteState>,
) -> (StatusCode, Json<BackendInfo>) {
    let uptime_seconds = state.program_start_time.elapsed().as_secs();

    let artnet_sending = state.dmx_renderer.lock().await.is_artnet_active();

    (
        StatusCode::OK,
        Json(BackendInfo {
            name: "ruhige_waldgeraeusche".to_owned(),
            version: env!("CARGO_PKG_VERSION").to_owned(),
            artnet_sending,
            uptime_seconds,
        }),
    )
}
#[allow(clippy::unused_async)]
pub async fn shutdown(State(_): State<RouteState>) -> StatusCode {
    if Command::new("sudo")
        .arg("shutdown")
        .arg("now")
        .status()
        .is_ok()
    {
        StatusCode::OK
    } else {
        StatusCode::INTERNAL_SERVER_ERROR
    }
}
#[allow(clippy::unused_async)]
pub async fn most_recent_key_id(
    State(state): State<RouteState>,
) -> (StatusCode, Json<u16>) {
    state
        .input_parser
        .lock()
        .await
        .most_recent_input_activated
        .map_or(
            (StatusCode::NO_CONTENT, Json(0)),
            |most_recent_input_activated| {
                (StatusCode::OK, Json(most_recent_input_activated))
            },
        )
}

#[allow(clippy::unused_async)]
pub async fn upload_audiofile(
    State(state): State<RouteState>,
    mut multipart: Multipart,
) -> StatusCode {
    while let Ok(next_field) = multipart.next_field().await {
        if let Some(field_content) = next_field {
            if let Some(field_name) = field_content.name() {
                if field_name != "audiofile" {
                    continue;
                }

                let file_name = if let Some(file_name) =
                    field_content.file_name()
                {
                    let sanitized_name = sanitize_filename(file_name);
                    if sanitized_name.is_empty() || sanitized_name == "_" {
                        logging::log(
                                format!("Filename '{file_name}' could not be sanitized to a valid name"),
                                logging::LogLevel::Warning,
                                false,
                            );
                        return StatusCode::UNPROCESSABLE_ENTITY;
                    }
                    sanitized_name
                } else {
                    return StatusCode::UNPROCESSABLE_ENTITY;
                };
                if let Ok(data) = field_content.bytes().await {
                    state
                        .db_handler
                        .lock()
                        .await
                        .save_audiofile(&file_name.clone(), &data);

                    return StatusCode::CREATED;
                }
                return StatusCode::UNPROCESSABLE_ENTITY;
            }
        }
    }
    StatusCode::NOT_ACCEPTABLE
}
pub async fn download_audiofile(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> impl IntoResponse {
    let audiofile = state.db_handler.lock().await.get_audiofile(id);
    let headers = [
        (header::CONTENT_TYPE, ".mp3"),
        (
            header::CONTENT_DISPOSITION,
            &format!("attachment; filename=\"{}\"", audiofile.0),
        ),
    ];
    (headers, audiofile.1).into_response()
}
pub async fn get_available_audiofile_names(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<(usize, String)>>) {
    (
        StatusCode::OK,
        Json(state.db_handler.lock().await.available_audiofile_names()),
    )
}

pub async fn delete_audiofile(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().await.remove_audiofile(id);
    StatusCode::OK
}

pub async fn sync_audiofiles(State(state): State<RouteState>) -> StatusCode {
    let mut db_handler = state.db_handler.lock().await;

    let available_audiofile_names = db_handler.available_audiofile_names();
    sync_audiofiles_with_filesystem(
        &mut db_handler.db_connection(),
        &available_audiofile_names,
    );
    StatusCode::OK
}

pub async fn get_dashboard(
    State(state): State<RouteState>,
) -> (StatusCode, Json<String>) {
    serde_json::to_string(&(*state.dashboard.lock().await)).map_or(
        (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new())),
        |serialized_dashboard| (StatusCode::OK, Json(serialized_dashboard)),
    )
}

pub async fn get_dmx_universe(
    State(state): State<RouteState>,
    Path(id): Path<u16>,
) -> (StatusCode, Json<Vec<u8>>) {
    let dmx_channels = {
        let mut db_handler = state.db_handler.lock().await;
        let mut dmx_renderer = state.dmx_renderer.lock().await;
        let mut input_parser = state.input_parser.lock().await;

        dmx_renderer.build_dmx_universe(
            id,
            &mut db_handler,
            &mut input_parser,
            (Duration::ZERO, Duration::ZERO),
        )
    };
    (StatusCode::OK, Json(dmx_channels))
}

pub async fn handle_keyevent(
    State(state): State<RouteState>,
    Query(keys): Query<Vec<RawInput>>,
) -> StatusCode {
    for input in keys {
        state.input_parser.lock().await.push_to_inputs(input);
    }
    StatusCode::OK
}

#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct InstructionsWithValue {
    instructions: Vec<Instruction>,
    value: usize,
}
pub async fn process_one_time_instructions(
    State(state): State<RouteState>,
    result: Result<Json<InstructionsWithValue>, JsonRejection>,
) -> StatusCode {
    match deserialize_json_body(
        result,
        &("/process_one_time_instructions", Method::PUT),
    ) {
        Ok(e) => {
            state
                .input_parser
                .lock()
                .await
                .push_one_time_instructions(&e.instructions, e.value);
            StatusCode::OK
        }
        Err(_) => StatusCode::BAD_REQUEST,
    }
}

pub async fn get_fixtures(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<DmxFixtureFileDescriptor>>) {
    let fixtures = DbHandler::fixtures_for_active_show(
        &mut state.db_handler.lock().await.db_connection(),
    );
    (StatusCode::OK, Json(fixtures))
}

pub async fn get_positions(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<ComposedPanTiltPosition>>) {
    let db_handler = state.db_handler.lock().await;
    (
        StatusCode::OK,
        Json(db_handler.active_show.positions.clone()),
    )
}

#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct PanTiltPositionRenamePayload {
    id: usize,
    name: String,
}
pub async fn rename_position(
    State(state): State<RouteState>,
    result: Result<Json<PanTiltPositionRenamePayload>, JsonRejection>,
) -> StatusCode {
    match deserialize_json_body(
        result,
        &("/rename_pan_tilt_position", Method::PATCH),
    ) {
        Ok(e) => {
            let mut db_handler = state.db_handler.lock().await;
            db_handler.rename_pan_tilt_position(e.id, &e.name);
            StatusCode::OK
        }
        Err(_) => StatusCode::BAD_REQUEST,
    }
}
#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct PanTiltPositionDeletePayload {
    id: usize,
}
pub async fn delete_position(
    State(state): State<RouteState>,
    result: Result<Json<PanTiltPositionDeletePayload>, JsonRejection>,
) -> StatusCode {
    match deserialize_json_body(
        result,
        &("/delete_pan_tilt_position", Method::DELETE),
    ) {
        Ok(e) => {
            let mut db_handler = state.db_handler.lock().await;
            db_handler.delete_pan_tilt_position(e.id);
            StatusCode::OK
        }
        Err(_) => StatusCode::BAD_REQUEST,
    }
}
#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct PanTiltPositionPayload {
    position_name: String,
}
pub async fn trigger_pan_tilt_position_creation(
    State(state): State<RouteState>,
    result: Result<Json<PanTiltPositionPayload>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/createpantiltposition", Method::POST),
    ) {
        Ok(e) => {
            {
                let mut db_handler = state.db_handler.lock().await;
                let mut dmx_renderer = state.dmx_renderer.lock().await;
                db_handler.trigger_pan_tilt_position_creation(
                    &mut dmx_renderer,
                    e.position_name.as_str(),
                );
            }
            (StatusCode::OK, "Ok".to_string())
        }
        Err(err) => err,
    }
}
pub async fn trigger_pan_tilt_position_update(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    let mut db_handler = state.db_handler.lock().await;
    let mut dmx_renderer = state.dmx_renderer.lock().await;

    db_handler.trigger_pan_tilt_position_update(&mut dmx_renderer, id);

    StatusCode::OK
}

pub async fn post_fixture(
    State(state): State<RouteState>,
    result: Result<Json<DmxFixtureFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixture", Method::POST)) {
        Ok(payload) => {
            state.db_handler.lock().await.create_fixture(&payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_fixture(
    State(state): State<RouteState>,
    result: Result<Json<DmxFixtureFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixture", Method::PATCH)) {
        Ok(payload) => {
            state.db_handler.lock().await.update_fixture(&payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn delete_fixture(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    state.db_handler.lock().await.delete_fixture(id);
    (StatusCode::OK, String::new())
}

pub async fn get_snippets_dir(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Directory<Snippet>>) {
    let db_handler = state.db_handler.lock().await;
    db_handler.active_show.snippets_dir.clone().map_or_else(
        || {
            logging::log(
                "No snippets are found in show file".to_string(),
                logging::LogLevel::Warning,
                true,
            );
            (StatusCode::NO_CONTENT, Json(Directory::default()))
        },
        |snippets| (StatusCode::OK, Json(snippets)),
    )
}
pub async fn put_new_default_snippets_dir(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state
        .db_handler
        .lock()
        .await
        .create_default_snippet_directory(id);
    (StatusCode::OK, Json(String::new()))
}
pub async fn delete_snippets_dir(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state.db_handler.lock().await.delete_snippets_dir(id);
    (StatusCode::OK, Json(String::new()))
}
pub async fn delete_snippet(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state.db_handler.lock().await.delete_snippet(id);
    (StatusCode::OK, Json(String::new()))
}
pub async fn put_new_default_snippet(
    State(state): State<RouteState>,
    Path(parent_id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state
        .db_handler
        .lock()
        .await
        .create_default_snippet(parent_id);
    (StatusCode::OK, Json(String::new()))
}
pub async fn patch_snippets_dir_name(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets_dir/:id/name", Method::PATCH),
    ) {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .set_snippets_dir_name(id, &payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_snippet_name(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/snippets/:id/name", Method::PATCH))
    {
        Ok(payload) => {
            state.db_handler.lock().await.set_snippet_name(id, &payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_snippet_instructions(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<Vec<Instruction>>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/snippets/:id/name", Method::PATCH))
    {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .set_snippet_instructions(id, &payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_snippet_category(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<SnippetCategory>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/category", Method::PATCH),
    ) {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .set_snippet_category(id, payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_snippet_do_not_use_instructions(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<bool>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/do_not_use_instructions", Method::PATCH),
    ) {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .set_snippet_do_not_use_instructions(id, payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_snippet_serial_module_key(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<Option<u16>>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/serial_module_key", Method::PATCH),
    ) {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .set_snippet_serial_module_key(id, payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn patch_snippet_requires_user_action_reason(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<Option<String>>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/requires_user_action_reason", Method::PATCH),
    ) {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .set_snippet_requires_user_action_reason(id, &payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}

pub async fn patch_snippet_parent_directory(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<usize>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/snippets/:id/move", Method::PATCH))
    {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .move_snippet_to_directory(id, payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}

pub async fn patch_snippet_directory_parent_directory(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<usize>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets_dir/:id/move", Method::PATCH),
    ) {
        Ok(payload) => {
            state
                .db_handler
                .lock()
                .await
                .move_snippet_directory_to_directory(id, payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}

pub async fn request_snippet_state_reset(
    State(state): State<RouteState>,
) -> StatusCode {
    state.db_handler.lock().await.request_snippet_state_reset();

    state
        .dmx_renderer
        .lock()
        .await
        .reset_all_fixtures_to_defaults();

    StatusCode::OK
}

pub async fn get_blueprints(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<BlueprintFileDescriptor>>) {
    let db_handler = state.db_handler.lock().await;
    (
        StatusCode::OK,
        Json(db_handler.active_show.blueprints.clone()),
    )
}
pub async fn post_blueprint(
    State(state): State<RouteState>,
) -> (StatusCode, String) {
    state
        .db_handler
        .lock()
        .await
        .create_default_blueprint_in_active_show();
    (StatusCode::OK, String::new())
}
pub async fn delete_blueprint(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().await.delete_blueprint(id);
    StatusCode::OK
}
pub async fn patch_blueprint(
    State(state): State<RouteState>,
    result: Result<Json<BlueprintFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/blueprint", Method::POST)) {
        Ok(payload) => {
            state.db_handler.lock().await.update_blueprint(&payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn get_timecodes(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<TimecodeFileDescriptor>>) {
    let db_handler = state.db_handler.lock().await;
    (
        StatusCode::OK,
        Json(db_handler.active_show.timecodes.clone()),
    )
}
pub async fn post_timecode(
    State(state): State<RouteState>,
) -> (StatusCode, String) {
    state
        .db_handler
        .lock()
        .await
        .create_default_timecode_in_active_show();
    (StatusCode::OK, String::new())
}
pub async fn delete_timecode(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().await.delete_timecode(id);
    StatusCode::OK
}
pub async fn patch_timecode(
    State(state): State<RouteState>,
    result: Result<Json<TimecodeFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/timecode", Method::POST)) {
        Ok(payload) => {
            state.db_handler.lock().await.update_timecode(&payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn get_available_show_names_with_active_hint(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<(usize, String, bool)>>) {
    let mut db_handler = state.db_handler.lock().await;
    (
        StatusCode::OK,
        Json(db_handler.get_available_show_names_with_active_hint()),
    )
}
pub async fn set_active_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    {
        let mut db_handler = state.db_handler.lock().await;
        db_handler.switch_show_to(id);
    }
    {
        let mut dmx_renderer = state.dmx_renderer.lock().await;
        dmx_renderer.reset_to_defaults();
    }
    {
        let mut input_parser = state.input_parser.lock().await;
        input_parser.reset_to_defaults();
    }
    (StatusCode::OK, String::new())
}
#[derive(Debug, Deserialize)]
pub struct RenameShowQuery {
    new_name: String,
}
pub async fn rename_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    Query(rename_show_query): Query<RenameShowQuery>,
) -> StatusCode {
    let mut db_handler = state.db_handler.lock().await;
    db_handler.rename_show(id, &rename_show_query.new_name);
    StatusCode::OK
}
#[allow(clippy::significant_drop_tightening)]
pub async fn get_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    {
        let mut db_handler = state.db_handler.lock().await;
        let show = db_handler.get_show(id);
        if let Ok(serialized_show) = serde_json::to_string(show) {
            return (StatusCode::OK, Json(serialized_show));
        }
    }
    (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new()))
}
pub async fn post_show(
    State(state): State<RouteState>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/show", Method::POST)) {
        Ok(payload) => {
            {
                let mut db_handler = state.db_handler.lock().await;
                db_handler.create_new_show(&payload);
            }
            {
                let mut dmx_renderer = state.dmx_renderer.lock().await;
                dmx_renderer.reset_to_defaults();
            }
            {
                let mut input_parser = state.input_parser.lock().await;
                input_parser.reset_to_defaults();
            }
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}
pub async fn upload_show(
    State(state): State<RouteState>,
    result: Result<Json<Show>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/show", Method::POST)) {
        Ok(payload) => {
            {
                let mut db_handler = state.db_handler.lock().await;
                db_handler.upload_show(&payload);
            }
            {
                let mut dmx_renderer = state.dmx_renderer.lock().await;
                dmx_renderer.reset_to_defaults();
            }
            {
                let mut input_parser = state.input_parser.lock().await;
                input_parser.reset_to_defaults();
            }
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}

pub async fn upload_snippet(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    mut multipart: Multipart,
) -> (StatusCode, Json<String>) {
    while let Ok(next_field) = multipart.next_field().await {
        if let Some(field_content) = next_field {
            if let Some(field_name) = field_content.name() {
                if field_name != "snippet" {
                    continue;
                }

                if let Ok(data) = field_content.bytes().await {
                    let Ok(json_str) = String::from_utf8(data.to_vec()) else {
                        return (
                            StatusCode::UNPROCESSABLE_ENTITY,
                            Json("Invalid UTF-8 content".to_string()),
                        );
                    };

                    let snippet: Snippet = match serde_json::from_str(&json_str)
                    {
                        Ok(s) => s,
                        Err(err) => {
                            logging::log(
                                format!(
                                    "Failed to parse snippet JSON: {err:?}"
                                ),
                                logging::LogLevel::Warning,
                                false,
                            );
                            return (
                                StatusCode::UNPROCESSABLE_ENTITY,
                                Json(format!("Invalid snippet JSON: {err}")),
                            );
                        }
                    };

                    let mut db_handler = state.db_handler.lock().await;
                    let snippet_name = snippet.name.clone();
                    match db_handler.create_snippet_with_data(snippet, id) {
                        Some(_) => {
                            return (
                                StatusCode::CREATED,
                                Json(format!(
                                    "Snippet '{snippet_name}' uploaded successfully"
                                )),
                            );
                        }
                        None => {
                            return (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json("Failed to create snippet".to_string()),
                            );
                        }
                    }
                }
                return (
                    StatusCode::UNPROCESSABLE_ENTITY,
                    Json("Invalid file content".to_string()),
                );
            }
        }
    }
    (
        StatusCode::NOT_ACCEPTABLE,
        Json("No valid snippet file found".to_string()),
    )
}

pub async fn delete_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    let mut db_handler = state.db_handler.lock().await;
    db_handler.delete_show(id);
    StatusCode::OK
}

pub async fn get_variable(
    State(state): State<RouteState>,
    Path(name): Path<String>,
) -> (StatusCode, Json<Option<usize>>) {
    let input_parser = state.input_parser.lock().await;
    input_parser
        .get_variable(&name, None)
        .map_or((StatusCode::NOT_FOUND, Json(None)), |variable| {
            (StatusCode::OK, Json(Some(variable.value)))
        })
}

pub async fn patch_variable(
    State(state): State<RouteState>,
    Path(name): Path<String>,
    result: Result<Json<usize>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/variables/:name", Method::PATCH)) {
        Ok(payload) => {
            let mut input_parser = state.input_parser.lock().await;
            match input_parser.get_variable_mut(&name, None) {
                Some(variable) => {
                    variable.value = payload;
                    (StatusCode::OK, String::new())
                }
                None => {
                    (StatusCode::NOT_FOUND, "Variable not found".to_string())
                }
            }
        }
        Err(err) => err,
    }
}

pub async fn get_fixturegroups(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<FixtureGroup>>) {
    let db_handler = state.db_handler.lock().await;
    (
        StatusCode::OK,
        Json(db_handler.active_show.enriched_groups()),
    )
}

pub async fn post_fixturegroup(
    State(state): State<RouteState>,
    result: Result<Json<FixtureGroup>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixturegroup", Method::POST)) {
        Ok(payload) => {
            state.db_handler.lock().await.create_fixturegroup(&payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}

pub async fn patch_fixturegroup(
    State(state): State<RouteState>,
    result: Result<Json<FixtureGroup>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixturegroup", Method::PATCH)) {
        Ok(payload) => {
            state.db_handler.lock().await.update_fixturegroup(&payload);
            (StatusCode::OK, String::new())
        }
        Err(err) => err,
    }
}

pub async fn delete_fixturegroup(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    state.db_handler.lock().await.delete_fixturegroup(id);
    (StatusCode::OK, String::new())
}

#[derive(Debug, Deserialize, Serialize)]
pub struct GdtfLoginRequest {
    pub user: String,
    pub password: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct GdtfLoginResponseRelevantIems {
    #[serde(rename = "set-cookie")]
    pub set_cookie: String,
}

pub async fn gdtf_authenticate(
    State(_): State<RouteState>,
    result: Result<Json<GdtfLoginRequest>, JsonRejection>,
) -> (StatusCode, Json<String>) {
    match deserialize_json_body(result, &("/gdtf/login", Method::POST)) {
        Ok(payload) => {
            let client = reqwest::Client::new();
            let api_url = "https://gdtf-share.com/apis/public/login.php";

            match client
                .post(api_url)
                .json(&payload)
                .send()
                .await
                .map_err(|e| e.to_string())
            {
                Ok(response) => {
                    if response.status().is_success() {
                        let Some(set_cookie) =
                            response.headers().get("set-cookie")
                        else {
                            return (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json(
                                    "cookie in gdtf response was missing"
                                        .to_owned(),
                                ),
                            );
                        };
                        let Ok(set_cookie) = set_cookie.to_str() else {
                            return (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json(
                                    "cookie in gdtf response was not valid utf8"
                                        .to_owned(),
                                ),
                            );
                        };
                        let Some(cookie) = set_cookie.split(';').next() else {
                            return (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                Json(
                                    "cookie in gdtf response has bad format"
                                        .to_owned(),
                                ),
                            );
                        };
                        (StatusCode::OK, Json(cookie.to_owned()))
                    } else {
                        (StatusCode::UNAUTHORIZED, Json(String::new()))
                    }
                }
                Err(err) => (StatusCode::NOT_FOUND, Json(err)),
            }
        }
        Err(err) => (err.0, Json(err.1)),
    }
}

pub async fn gdtf_get_list(
    State(_): State<RouteState>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, Json<String>) {
    match deserialize_json_body(result, &("/gdtf/get-list", Method::POST)) {
        Ok(cookie) => {
            let client = reqwest::Client::new();
            let api_url = "https://gdtf-share.com/apis/public/getList.php";

            match client.get(api_url).header("Cookie", cookie).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        response.text().await.map_or_else(
                            |_| {
                                (
                                    StatusCode::INTERNAL_SERVER_ERROR,
                                    Json(String::new()),
                                )
                            },
                            |text| (StatusCode::OK, Json(text)),
                        )
                    } else if response.status().is_client_error() {
                        (StatusCode::UNAUTHORIZED, Json(String::new()))
                    } else {
                        (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new()))
                    }
                }
                Err(_) => {
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new()))
                }
            }
        }
        Err(err) => (err.0, Json(String::new())),
    }
}

pub async fn get_fixture_from_gdtf_share(
    State(_): State<RouteState>,
    Path((rid, mode)): Path<(usize, String)>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, Json<Option<GdtfFixtureFileDescriptor>>) {
    match deserialize_json_body(
        result,
        &("/gdtf-share/fixture/:rid/mode/:mode", Method::POST),
    ) {
        Ok(cookie) => {
            let client = reqwest::Client::new();
            let api_url = "https://gdtf-share.com/apis/public/downloadFile.php";

            match client
                .get(api_url)
                .header("Cookie", cookie)
                .query(&[("rid", rid)])
                .send()
                .await
            {
                Ok(response) => {
                    if response.status().is_success() {
                        response.bytes().await.map_or_else(
                            |_| {
                                (
                                    StatusCode::INTERNAL_SERVER_ERROR,
                                    Json(None),
                                )
                            },
                            |bytes| {
                                let cursor = Cursor::new(bytes.to_vec());
                                if let Ok(parsed_gdtf_file) =
                                    gdtf_parser::Gdtf::try_from(cursor)
                                {
                                    if let Ok(mode) =  gdtf_parser::utils::units::name::Name::new(&mode){
                                        let Ok(fixture) =
                                            GdtfFixtureFileDescriptor::from_parsed_gdtf_file(
                                                parsed_gdtf_file.fixture_type,
                                                &mode
                                            )else {
                                                return (StatusCode::INTERNAL_SERVER_ERROR, Json(None));
                                            };
                                        (StatusCode::OK, Json(Some(fixture)))
                                    } else {
                                        (StatusCode::BAD_REQUEST, Json(None))
                                    }
                                } else {
                                    (StatusCode::INTERNAL_SERVER_ERROR, Json(None))
                                }
                            },
                        )
                    } else if response.status()
                        == reqwest::StatusCode::UNAUTHORIZED
                    {
                        (StatusCode::UNAUTHORIZED, Json(None))
                    } else {
                        (StatusCode::INTERNAL_SERVER_ERROR, Json(None))
                    }
                }
                Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(None)),
            }
        }
        Err(err) => (err.0, Json(None)),
    }
}
