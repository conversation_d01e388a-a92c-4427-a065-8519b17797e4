<script lang="ts">
    let {
        value = $bindable(),
        name,
        placeholder,
        maxlength,
        disabled,
        onchange,
    }: {
        name?: string;
        value?: string;
        placeholder?: string;
        maxlength?: number;
        disabled?: boolean;
        onchange?: (e: string) => any;
    } = $props();
</script>

<input
    name={name ?? "textinput"}
    type="text"
    class="w-full rounded-lg border border-input bg-input px-4 py-1 text-primary drop-shadow focus:outline-none focus:ring-2 focus:ring-accent disabled:text-disabled"
    bind:value
    {placeholder}
    {maxlength}
    {disabled}
    onchange={(e) =>
        onchange && e.target
            ? onchange(
                  // @ts-ignore -> value is there, but typescript is complaining
                  e.target.value,
              )
            : null}
/>
