<script lang="ts">
    import Textinput from "$lib/atoms/textinput.svelte";
    let {
        value = $bindable(),
        label,
        placeholder,
        maxlength,
        disabled,
        onchange,
    }: {
        value: string | undefined;
        label: string;
        placeholder?: string;
        maxlength?: number;
        disabled?: boolean;
        onchange?: () => void;
    } = $props();
</script>

<div class="flex flex-col">
    <label for={label}>{label}</label>
    <Textinput
        name={label}
        bind:value
        {placeholder}
        {maxlength}
        {onchange}
        {disabled}
    />
</div>
