<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import PasswordInput from "$lib/atoms/passwordinput.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import { TOAST } from "$lib/stores/toast";
    import { teleportToBody } from "$lib/useActions/teleport.svelte";
    import { onMount } from "svelte";
    import { get } from "svelte/store";
    import { networking } from "$lib/stores/networking";
    import Loadingspinner from "$lib/atoms/Loadingspinner.svelte";
    import { type GdtfFixtureFileDescriptor } from "$lib/types/bindings/GdtfFixtureFileDescriptor";

    interface GdtfShareListFixture {
        rid: number;
        fixture: string;
        manufacturer: string;
        revision: string;
        creationDate: number;
        lastModified: number;
        uploader: string;
        rating: number;
        version: string;
        creator: string;
        uuid: string;
        filesize: number;
        modes: {
            name: string;
            dmxfootprint: number;
        }[];
    }

    let {
        onFixtureSelected,
    }: { onFixtureSelected: (fixture: GdtfFixtureFileDescriptor) => any } =
        $props();

    let gdtfUsername: string | undefined = $state();
    let gdtfPassword: string | undefined = $state();

    onMount(() => {
        if (typeof localStorage !== "undefined") {
            if (
                localStorage.getItem("gdtf-share-username") &&
                localStorage.getItem("gdtf-share-password")
            ) {
                gdtfUsername =
                    localStorage.getItem("gdtf-share-username") || undefined;
                gdtfPassword =
                    localStorage.getItem("gdtf-share-password") || undefined;
            }
            if (localStorage.getItem("gdtf-share-cookie")) {
                gdtfCookie =
                    localStorage.getItem("gdtf-share-cookie") || undefined;
            }
        }
    });

    let searchQuery = $state("");
    let isLoading = $state(false);
    let searchResults: { list: GdtfShareListFixture[]; result: boolean } =
        $state({ list: [], result: false });
    let showGdtfCredentialsModal = $state(false);
    let rememberCredentials = $state(false);
    let showGdtfSearchModal = $state(false);
    let gdtfCookie: string | undefined = $state();

    async function gdtfAuthenticate(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (gdtfUsername === undefined || gdtfPassword === undefined) {
                showGdtfCredentialsModal = true;
                return;
            } else if (gdtfUsername === "" || gdtfPassword === "") {
                showGdtfCredentialsModal = true;
                TOAST.error("Username and password cannot be empty");
                return;
            }

            if (rememberCredentials) {
                localStorage.setItem("gdtf-share-username", gdtfUsername);
                localStorage.setItem("gdtf-share-password", gdtfPassword);
                rememberCredentials = false;
            }

            try {
                isLoading = true;
                fetch(
                    `http://${get(networking)}:${networking.port}/gdtf-share/authenticate`,
                    {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            user: gdtfUsername,
                            password: gdtfPassword,
                        }),
                    },
                )
                    .then(async (response) => {
                        if (!response.ok) {
                            throw new Error(
                                `HTTP error! status: ${response.status}`,
                            );
                        }
                        const data = await response.json();
                        showGdtfCredentialsModal = false;
                        localStorage.setItem("gdtf-share-cookie", data);
                        gdtfCookie = data;
                        isLoading = false;
                        resolve();
                    })
                    .catch((error) => {
                        showGdtfCredentialsModal = true;
                        reject(error);
                        return;
                    });
            } catch (error) {
                isLoading = false;
                reject(error);
            }
        });
    }

    function openGdtfSearchModal() {
        if (searchResults.list.length > 0) {
            showGdtfSearchModal = true;
            return;
        }

        showGdtfSearchModal = true;
        searchQuery = "";

        try {
            isLoading = true;
            fetch(
                `http://${get(networking)}:${networking.port}/gdtf-share/get-list`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(gdtfCookie),
                },
            )
                .then(async (response) => {
                    if (response.status === 401) {
                        gdtfAuthenticate().then(() => {
                            openGdtfSearchModal();
                        });
                    } else {
                        const data = await response.json();
                        const result = JSON.parse(data);
                        TOAST.success("Successfully retrieved fixture list");
                        searchResults = result;
                        isLoading = false;
                    }
                })
                .catch((error) => {
                    if (error.status === 401) {
                        gdtfAuthenticate().then(() => {
                            openGdtfSearchModal();
                        });
                    }
                    TOAST.error(`Search failed with ${error}`);
                    return;
                });
        } catch (error) {
            TOAST.error("Search failed");
        }
    }

    function loadFixtureFromGdtfShare(
        fixture: GdtfShareListFixture,
        mode: string,
    ) {
        try {
            isLoading = true;
            fetch(
                `http://${get(networking)}:${networking.port}/gdtf-share/fixture/${fixture.rid}/mode/${mode}`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(gdtfCookie),
                },
            )
                .then(async (response) => {
                    if (response.status === 200) {
                        const data: GdtfFixtureFileDescriptor =
                            await response.json();
                        TOAST.success("Successfully retrieved fixture");
                        onFixtureSelected(data);
                    } else if (response.status === 401) {
                        gdtfAuthenticate().then(() => {
                            loadFixtureFromGdtfShare(fixture, mode);
                        });
                    } else {
                        TOAST.error("Download failed");
                    }
                    isLoading = false;
                    showGdtfSearchModal = false;
                })
                .catch((error) => {
                    TOAST.error(`Download failed with ${error}`);
                    return;
                });
        } catch (error) {
            TOAST.error(`Download failed with ${error}`);
        }
    }
</script>

<div class="rounded-lg bg-object p-4">
    <h3 class="mb-4 text-lg font-semibold">GDTF-Share</h3>
    <Button
        id="gdtf-auth-btn"
        onclick={async () => {
            if (gdtfCookie !== undefined) {
                openGdtfSearchModal();
            } else {
                gdtfAuthenticate()
                    .then(() => {
                        openGdtfSearchModal();
                    })
                    .catch((error) => {
                        TOAST.error(`Authentication failed with ${error}`);
                    });
            }
        }}
        disabled={isLoading}
    >
        <div class="flex items-center">
            <img
                class="bg-black p-4"
                src="gdtf-mvr-logo.png"
                alt="GDTF-Share"
            />
            {#if isLoading}
                <Loadingspinner />
            {/if}
        </div>
    </Button>
</div>

<dialog
    open={showGdtfCredentialsModal}
    class="absolute top-0 bg-black/50 w-full h-screen"
    use:teleportToBody
>
    <div class="flex justify-center h-full">
        <div class="flex flex-col justify-center">
            <div
                class="rounded-lg border border-input bg-object p-4 shadow-2xl min-w-96"
            >
                <button
                    class="flex w-full justify-end mb-2"
                    onclick={() => (showGdtfCredentialsModal = false)}
                >
                    <Icon icon="mdi:close"></Icon>
                </button>

                <div class="flex justify-center mb-4">
                    <img
                        class="bg-black p-4"
                        src="gdtf-mvr-logo.png"
                        alt="GDTF-Share Credentials"
                    />
                </div>

                <div class="mb-4">
                    <LabeledTextinput
                        label="Username"
                        placeholder="my-GDTF-Share-username"
                        bind:value={gdtfUsername}
                    ></LabeledTextinput>
                    <label for="password">Password</label>
                    <PasswordInput
                        placeholder="my-GDTF-Share-password"
                        bind:value={gdtfPassword}
                    ></PasswordInput>

                    <div class="flex pt-2 items-center space-x-2">
                        <p>Remember credentials</p>
                        <input
                            type="checkbox"
                            bind:checked={rememberCredentials}
                        />
                    </div>

                    <div class="pt-4">
                        <button
                            type="submit"
                            onclick={() => {
                                if (gdtfCookie !== undefined) {
                                    openGdtfSearchModal();
                                } else {
                                    gdtfAuthenticate()
                                        .then(() => {
                                            openGdtfSearchModal();
                                        })
                                        .catch((error) => {
                                            TOAST.error(
                                                `Authentication failed with ${error}`,
                                            );
                                        });
                                }
                            }}
                            class="rounded-lg border border-input p-6 hover:bg-primary/5 flex justify-center space-x-2 w-full"
                        >
                            <p>Authenticate</p>
                            <Icon icon="hugeicons:authorized"></Icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</dialog>

<dialog
    open={showGdtfSearchModal}
    class="absolute top-0 bg-black/50 w-full h-screen"
    use:teleportToBody
>
    <div class="flex justify-center h-full">
        <div class="flex flex-col justify-center">
            <div class="w-fit p-8 rounded-lg bg-object">
                {#if isLoading}
                    Loading fixture list from GDTF-Share...
                    <Loadingspinner />
                {:else}
                    <button
                        class="flex w-full justify-end mb-2"
                        onclick={() => {
                            showGdtfSearchModal = false;
                        }}
                    >
                        <Icon icon="mdi:close"></Icon>
                    </button>
                    <div class="mb-4">
                        <div class="flex space-x-2">
                            <div class="flex-1">
                                <LabeledTextinput
                                    label="Search fixtures"
                                    bind:value={searchQuery}
                                    placeholder="Enter fixture name, manufacturer, or keywords..."
                                />
                            </div>
                        </div>
                    </div>
                    {#if searchResults.list.length > 0}
                        <div
                            class="rounded-lg border border-input bg-object p-4"
                        >
                            <div
                                class="max-h-[75vh] max-w-[90vw] overflow-y-auto"
                            >
                                <table class="text-sm">
                                    <thead>
                                        <tr class="border-b border-input">
                                            <th class="text-left py-2 px-2">
                                                Fixture Name
                                            </th>
                                            <th class="text-left py-2 px-2">
                                                Manufacturer
                                            </th>
                                            <th class="text-left py-2 px-2"
                                                >Revision</th
                                            >
                                            <th class="text-left py-2 px-2"
                                                >Creation</th
                                            >
                                            <th class="text-left py-2 px-2"
                                                >Last Modified</th
                                            >
                                            <th class="text-left py-2 px-2"
                                                >Uploader</th
                                            >
                                            <th class="text-left py-2 px-2"
                                                >Rating</th
                                            >
                                            <th class="text-left py-2 px-2"
                                                >Version</th
                                            >
                                            <th class="text-left py-2 px-2"
                                                >Creator</th
                                            >
                                            <th class="text-center py-2 px-2"
                                                >Mode</th
                                            >
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {#each searchResults.list.filter((fixture) => fixture.fixture
                                                    ?.toLowerCase()
                                                    .includes(searchQuery.toLowerCase()) || fixture.manufacturer
                                                    ?.toLowerCase()
                                                    .includes(searchQuery.toLowerCase()) || fixture.revision
                                                    ?.toLowerCase()
                                                    .includes(searchQuery.toLowerCase()) || fixture.uploader
                                                    ?.toLowerCase()
                                                    .includes(searchQuery.toLowerCase()) || fixture.version
                                                    ?.toLowerCase()
                                                    .includes(searchQuery.toLowerCase()) || fixture.creator
                                                    ?.toLowerCase()
                                                    .includes(searchQuery.toLowerCase())) as fixture}
                                            <tr
                                                class="border-b border-input hover:bg-secondary"
                                            >
                                                <td class="py-2 px-2"
                                                    >{fixture.fixture}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.manufacturer}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.revision}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.creationDate}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.lastModified}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.uploader}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.rating}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.version}</td
                                                >
                                                <td class="py-2 px-2"
                                                    >{fixture.creator}</td
                                                >
                                                <td class="py-2 px-2">
                                                    {#each fixture.modes as dmxMode}
                                                        <button
                                                            class="underline cursor-pointer hover:text-primary/75"
                                                            onclick={() => {
                                                                loadFixtureFromGdtfShare(
                                                                    fixture,
                                                                    dmxMode.name,
                                                                );
                                                            }}
                                                        >
                                                            {dmxMode.name} ({dmxMode.dmxfootprint}ch)
                                                        </button>
                                                    {/each}
                                                </td>
                                            </tr>
                                        {/each}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {/if}
                {/if}
            </div>
        </div>
    </div>
</dialog>
