import { writable, get } from 'svelte/store';
import type { BlueprintFileDescriptor } from '$lib/types/bindings/BlueprintFileDescriptor';
import { shows } from './shows';
import { networking } from './networking';
import { PropertyFileDescriptor } from '$lib/types/bindings/PropertyFileDescriptor';
import type { InterpolationMethod } from '$lib/types/bindings/InterpolationMethod';

function createBlueprintStore() {
    const { subscribe, set } = writable<BlueprintFileDescriptor[]>([]);

    shows.subscribe(() => fetchBlueprints().then(data => set(data)));

    return {
        subscribe,
        patchOne: (blueprint: BlueprintFileDescriptor) =>
            patchBlueprint(blueprint).then(data => { set(data); return true }),
        createOne: () =>
            createBlueprint().then(data => set(data)),
        deleteOne: (blueprint: BlueprintFileDescriptor) =>
            deleteBlueprint(blueprint).then(data => set(data)),
    };
}

export const blueprints = createBlueprintStore();

export function appendBlueprintEditingReason(filterFn: (blueprint: BlueprintFileDescriptor) => boolean, reason: string) {
    let snippets_from_store = get(blueprints);
    snippets_from_store = snippets_from_store.filter(snippet => filterFn(snippet));
    snippets_from_store.forEach(blueprint => {
        if (blueprint.requires_user_action_reason?.length) {
            blueprint.requires_user_action_reason = blueprint.requires_user_action_reason?.concat(", ", reason)
        } else {
            blueprint.requires_user_action_reason = reason
        }
        blueprints.patchOne(blueprint)
    })
}

async function fetchBlueprints(): Promise<BlueprintFileDescriptor[]> {
    return new Promise(async (resolve) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/blueprints`);
            resolve(response.json());
        } else {
            setTimeout(async () => resolve(await fetchBlueprints()), 1000)
        }
    })
}

async function createBlueprint(
): Promise<BlueprintFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/blueprint`, { method: 'POST' });
    return fetchBlueprints();
}
async function patchBlueprint(
    blueprint: BlueprintFileDescriptor,
): Promise<BlueprintFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/blueprint`, {
        method: 'PATCH',
        body: JSON.stringify(blueprint),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchBlueprints();
}
async function deleteBlueprint(
    blueprint: BlueprintFileDescriptor,
): Promise<BlueprintFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/blueprint/${blueprint.id}`, {
        method: 'DELETE',
    });
    return fetchBlueprints();
}

export function interpolationMethod(property: PropertyFileDescriptor): InterpolationMethod | null {
    if ("UnimplementedChannel" in property) {
        return property.UnimplementedChannel[2]
    } else if ("PanTiltPositions" in property) {
        return property.PanTiltPositions[1]
    } else if ("ColorPropertyCoordinates" in property) {
        return property.ColorPropertyCoordinates[1]
    } else {
        return null
    }
}

export function updateInterpolationMethod(property: PropertyFileDescriptor, newMethod: InterpolationMethod) {
    if ("UnimplementedChannel" in property) {
        property.UnimplementedChannel[2] = newMethod
    } else if ("PanTiltPositions" in property) {
        property.PanTiltPositions[1] = newMethod
    } else if ("ColorPropertyCoordinates" in property) {
        property.ColorPropertyCoordinates[1] = newMethod
    }
}
