import { writable } from 'svelte/store';
import type { Dashboard } from '$lib/types/bindings/Dashboard'
import { get } from 'svelte/store';
import { networking } from './networking';

function createDashboardStore() {
    const { subscribe, set } = writable<Dashboard>();

    return {
        subscribe,
        fetch: async () => {
            const dashboard = await fetchDashboard();
            if (dashboard) {
                set(dashboard);
            }
            return dashboard;
        }
    };
}

export const dashboard = createDashboardStore();

async function fetchDashboard(): Promise<Dashboard | null> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            try {
                const response = await fetch(`http://${ip}:${networking.port}/dashboard`);
                const dashboardJson = await response.json();
                const parsedDashboard: Dashboard = JSON.parse(dashboardJson);
                resolve(parsedDashboard);
            } catch (error) {
                console.error('Failed to fetch dashboard:', error);
                resolve(null);
            }
        }
    });
}
