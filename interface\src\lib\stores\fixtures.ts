import { writable } from 'svelte/store';
import type { DmxFixtureFileDescriptor } from '../types/bindings/DmxFixtureFileDescriptor';
import { get } from 'svelte/store';
import { networking } from './networking';
import { shows } from './shows';

function createFixtureStore() {
    const { subscribe, set, update } = writable<DmxFixtureFileDescriptor[]>([]);

    shows.subscribe(() => fetchFixtures().then(data => set(data)));

    return {
        subscribe,
        set,
        updateLocal: update,
        updateRemote: async () => {
            const data = await updateRemote();
            set(data);
            return data;
        },
        create: async (fixtures: DmxFixtureFileDescriptor[]) => {
            const data = await createFixtures(fixtures);
            set(data);
            return data;
        },
        delete: async (fixture: DmxFixtureFileDescriptor) => {
            const data = await deleteFixture(fixture);
            set(data);
            return data;
        },
    };
}

export const fixtures = createFixtureStore();

async function fetchFixtures(): Promise<DmxFixtureFileDescriptor[]> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/fixtures`);
            const unsorted_fixtures: DmxFixtureFileDescriptor[] = await response.json();
            let sorted_fixtures = unsorted_fixtures
                .sort((a, b) => {
                    const a_name_id = parseInt(
                        a.name.split("_").slice(-1)[0] ?? "0",
                    );
                    const b_name_id = parseInt(
                        b.name.split("_").slice(-1)[0] ?? "0",
                    );
                    return a_name_id - b_name_id;
                })
                .sort((a, b) => {
                    let a_name = weight(a.name.split("_")[0]);
                    let b_name = weight(b.name.split("_")[0]);
                    return a_name - b_name;
                });
            resolve(sorted_fixtures);
        } else {
            resolve([]);
        }
    });
}

function weight(stringToParse: string): number {
    let splittedString = stringToParse.split("");
    let sum = splittedString
        .map((str) => str.charCodeAt(0))
        .reduce((ps, a) => ps + a, 0);
    return sum;
}

async function updateRemote(): Promise<DmxFixtureFileDescriptor[]> {
    const fxs = get(fixtures);
    await Promise.all(fxs.map(async fixture => {
        await fetch(`http://${get(networking)}:${networking.port}/fixture`, {
            method: 'PATCH',
            body: JSON.stringify(fixture),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
    }));
    return fetchFixtures();
}

async function createFixtures(fixtures: DmxFixtureFileDescriptor[]): Promise<DmxFixtureFileDescriptor[]> {
    for (const fixture of fixtures) {
        await fetch(`http://${get(networking)}:${networking.port}/fixture`, {
            method: 'POST',
            body: JSON.stringify(fixture),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
    }
    return fetchFixtures();
}

async function deleteFixture(fixture: DmxFixtureFileDescriptor): Promise<DmxFixtureFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/fixture/${fixture.id}`, {
        method: 'DELETE',
    });
    return fetchFixtures();
}
