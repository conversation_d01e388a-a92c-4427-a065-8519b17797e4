import { writable, get } from 'svelte/store';
import { TOAST } from './toast';
import { shows } from './shows';

export interface KeyboardMapping {
    keyboardKey: string;
    serial_module_key: number;
}

const STORAGE_KEY = 'keyboard_mappings';

function createKeyboardMappingsStore() {
    const { subscribe, set, update } = writable<KeyboardMapping[]>([]);


    shows.subscribe((shows) => {
        if (shows.length && typeof localStorage !== 'undefined') {
            const stored = localStorage.getItem(STORAGE_KEY);
            if (stored) {
                try {
                    set(JSON.parse(stored))
                } catch (e) {
                    console.error('Failed to parse keyboard mappings from localStorage:', e);
                }
            }
        }
    });

    function saveToStorage(mappings: KeyboardMapping[]) {
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(mappings));
        }
    }

    return {
        subscribe,
        setMapping: (serial_module_key: number, keyboardKey: string | null) => {
            update(mappings => {
                const filtered = mappings.filter(m => m.serial_module_key !== serial_module_key);

                if (keyboardKey !== null && keyboardKey.length === 1) {
                    const possibleDublicate = filtered.find(m => m.keyboardKey === keyboardKey);
                    if (possibleDublicate) {
                        TOAST.error('Cannot assign the same key to multiple snippets.')
                    }
                    filtered.push({ keyboardKey, serial_module_key: serial_module_key });
                    saveToStorage(filtered);
                    TOAST.success('Key mapping saved.');
                    return filtered;
                } else {
                    saveToStorage(filtered);
                    TOAST.success('Please type a valid key.');
                    return filtered;
                }

            });
        },
        getKeyboardKeyFrom: (serial_module_key: number): string | null => {
            const mappings = get(keyboardMappings);
            const mapping = mappings.find(m => m.serial_module_key === serial_module_key);
            return mapping ? mapping.keyboardKey : null;
        },
        getserialModuleKeyFrom: (keyboardKey: string): number | null => {
            const mappings = get(keyboardMappings);
            const mapping = mappings.find(m => m.keyboardKey === keyboardKey);
            return mapping ? mapping.serial_module_key : null;
        },
    };
}

export const keyboardMappings = createKeyboardMappingsStore();
