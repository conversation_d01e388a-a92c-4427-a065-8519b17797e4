import { get, writable } from 'svelte/store';
import { invoke, isTauri } from '@tauri-apps/api/core';
import type { BackendInfo } from '../types/bindings/BackendInfo';

const PORT = '4000';
declare const __APP_VERSION__: string;

function createNetworkingStore() {
    const { subscribe, set } =
        writable<string | undefined>(
            undefined,
        );

    setInterval(async () => {
        const ip = get(networking);
        if (ip) {
            fetch(`http://${ip}:${PORT}/ping`)
                .then(async (response) => {
                    if (response.status !== 200) {
                        set(undefined)
                    } else {
                        const host_version = await response.json();
                        if (host_version !== __APP_VERSION__) {
                            set(undefined)
                        }
                    }
                })
                .catch(() => {
                    set(undefined)
                })
        }
    }, 5000);


    return {
        set,
        subscribe,
        port: PORT,
    };
}

export const networking = createNetworkingStore();

export type ReachableBackend = [string, BackendInfo]; // [ip, backend_info]

export async function allRwBackendsOnNetwork(): Promise<ReachableBackend[]> {
    return new Promise(async (resolve) => {
        if (isTauri()) {
            invoke('search_host_ips')
                .then((result) => {
                    resolve(result as ReachableBackend[])
                })
                .catch((_) => {
                    resolve([])
                });
        } else {
            const response = await fetch(`http://127.0.0.1:${PORT}/rw_discover`);
            const payload = await response.json();

            if (payload.name === 'ruhige_waldgeraeusche') {
                resolve([["127.0.0.1", payload]])
            } else {
                resolve([])
            }
        }
    })
}
