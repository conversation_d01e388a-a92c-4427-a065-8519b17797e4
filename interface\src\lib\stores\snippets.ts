import { writable, get } from 'svelte/store';
import type { Snippet } from '$lib/types/bindings/Snippet';
import type { Directory } from '$lib/types/bindings/Directory';
import type { Instruction } from '$lib/types/bindings/Instruction'
import { shows } from './shows';
import { networking } from './networking';
import { SnippetCategory } from '$lib/types/bindings/SnippetCategory';
import { TOAST } from './toast';

function createSnippetDirStore() {
    const { subscribe, set } = writable<Directory<Snippet>>({ id: 0, name: "default", content: [] });

    shows.subscribe(() => fetchSnippets().then(data => set(data)));

    return {
        subscribe,
        set,
        createDefaultSnippet: (parentId: number) => createDefaultSnippet(parentId).then(data => set(data)),
        setSnippetName: (snippetId: number, newName: string) => setSnippetName(snippetId, newName).then(data => set(data)),
        setSnippetSerialModuleKey: (snippetId: number, newKey: number | null) => setSnippetSerialModuleKey(snippetId, newKey).then(data => set(data)),
        setSnippetDoNotUseInstructions: (snippetId: number, newFlag: boolean) => setSnippetDoNotUseInstructions(snippetId, newFlag).then(data => set(data)),
        setSnippetCategory: (snippetId: number, newCategory: SnippetCategory) => setSnippetCategory(snippetId, newCategory).then(data => set(data)),
        setSnippetRequiresUserActionReason: (snippetId: number, newReason: string | null) => setSnippetRequiresUserActionReason(snippetId, newReason).then(data => set(data)),
        setSnippetInstructions: (snippetId: number, newInstructions: Instruction[]) => setSnippetInstructions(snippetId, newInstructions).then(data => set(data)),
        deleteSnippet: (snippetId: number) => deleteSnippet(snippetId).then(data => set(data)),
        createDefaultSnippetDirectory: (parentId: number) => createDefaultSnippetDirectory(parentId).then(data => set(data)),
        setSnippetDirectoryName: (directoryId: number, newName: string) => setSnippetDirectoryName(directoryId, newName).then(data => set(data)),
        deleteSnippetDirectory: (directoryId: number) => deleteSnippetDirectory(directoryId).then(data => set(data)),
        moveSnippetToDirectory: (snippetId: number, directoryId: number) => moveSnippetToDirectory(snippetId, directoryId).then(data => set(data)),
        uploadSnippet: (file: File, parentDirId: number) => uploadSnippet(file, parentDirId).then(data => set(data)),
        moveSnippetDirectoryToDirectory: (directoryId: number, newParentDirectoryId: number) => moveSnippetDirectoryToDirectory(directoryId, newParentDirectoryId).then(data => set(data))
    };
}

export const snippetDir = createSnippetDirStore();

async function createDefaultSnippet(
    parent_id: number
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${parent_id}`, { method: 'PUT' });
    return fetchSnippets();
}
async function setSnippetName(
    snippetId: number,
    newName: string
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/name`, {
        method: 'PATCH',
        body: JSON.stringify(newName),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function setSnippetCategory(
    snippetId: number,
    newCategory: SnippetCategory
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/category`, {
        method: 'PATCH',
        body: JSON.stringify(newCategory),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function setSnippetDoNotUseInstructions(
    snippetId: number,
    do_not_use_instructions: boolean
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/do_not_use_instructions`, {
        method: 'PATCH',
        body: JSON.stringify(do_not_use_instructions),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function setSnippetSerialModuleKey(
    snippetId: number,
    newSerialModuleKey: number | null
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/serial_module_key`, {
        method: 'PATCH',
        body: JSON.stringify(newSerialModuleKey),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function setSnippetRequiresUserActionReason(
    snippetId: number,
    newRequiresUserActionReason: string | null
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/requires_user_action_reason`, {
        method: 'PATCH',
        body: JSON.stringify(newRequiresUserActionReason),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function setSnippetInstructions(
    snippetId: number,
    instructions: Instruction[]
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/instructions`, {
        method: 'PATCH',
        body: JSON.stringify(instructions),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function deleteSnippet(
    snippetId: number,
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}`, { method: 'DELETE' });
    return fetchSnippets();
}
async function createDefaultSnippetDirectory(
    parent_id: number
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets_dir/${parent_id}`, { method: 'PUT' });
    return fetchSnippets();
}
async function setSnippetDirectoryName(
    directoryId: number,
    newName: string
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets_dir/${directoryId}/name`, {
        method: 'PATCH',
        body: JSON.stringify(newName),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
async function deleteSnippetDirectory(
    directoryId: number,
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets_dir/${directoryId}`, { method: 'DELETE' });
    return fetchSnippets();
}

export function appendSnippetEditingReason(filterFn: (snippet: Snippet) => boolean, reason: string) {
    let snippet_dir = get(snippetDir)
    let snippets_from_store = allSnippetsOf(snippet_dir).filter(snippet => filterFn(snippet));
    snippets_from_store.forEach(snippet => {
        if (snippet.requires_user_action_reason?.length) {
            snippetDir.setSnippetRequiresUserActionReason(
                snippet.id,
                snippet.requires_user_action_reason?.concat(", ", reason)
            );
        } else {
            snippetDir.setSnippetRequiresUserActionReason(snippet.id, reason);
        }
    })
}

export function allLeafInstructionsOf(snippetsDir: Directory<Snippet>, snippetFilter: (snippet: Snippet) => boolean): Instruction[] {
    const allInstructionsOfAllSnippets = allSnippetsOf(snippetsDir)
        .filter(snippetFilter)
        .map(snippet => snippet.instructions)
        .flat();

    let allLeafesOfAllSnippets: Instruction[] = [];
    allInstructionsOfAllSnippets.forEach(instruction => {
        allLeafesOfAllSnippets = [
            ...allLeafesOfAllSnippets,
            ...allLeafesOf(instruction),
        ];
    });
    return allLeafesOfAllSnippets;
}
export function allLeafesOf(instruction: Instruction): Instruction[] {
    let allLeafes = [];
    if (instruction.instruction === 'FixtureLoop') {
        allLeafes.push(
            instruction.instructionMod.fixtures.map(instruction =>
                allLeafesOf(instruction),
            ),
        );
        allLeafes.push(
            instruction.instructionMod.instructions.map(instruction =>
                allLeafesOf(instruction),
            ),
        );
    } else if (instruction.instruction === 'IfStatement') {
        instruction.instructionMod.forEach((conditionalBlock) => {
            allLeafes.push(conditionalBlock.content.map(instruction => allLeafesOf(instruction)))
        })
    } else if (instruction.instruction === 'IfQueueAllowsContinue') {
        allLeafes.push(
            instruction.instructionMod.map(instruction =>
                allLeafesOf(instruction),
            ),
        );
    } else if (instruction.instruction === 'AddToGroup') {
        allLeafes.push(
            instruction.instructionMod.fixtures.map(instruction =>
                allLeafesOf(instruction),
            ),
        );
    } else if (instruction.instruction === 'RemoveFromGroup') {
        allLeafes.push(
            instruction.instructionMod.fixtures.map(instruction =>
                allLeafesOf(instruction),
            ),
        );
    } else {
        return [instruction];
    }
    return allLeafes.flat().flat();
}

async function fetchSnippets(): Promise<Directory<Snippet>> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/snippets_dir`);
            resolve(response.json());
        } else {
            setTimeout(async () => resolve(await fetchSnippets()), 1000)
        }
    })
}

export function allSnippetsOf(directory: Directory<Snippet>): Snippet[] {
    let result: Snippet[][] = [];
    directory.content.forEach(item => {
        if ("Directory" in item) {
            result.push(allSnippetsOf(item.Directory))
        } else {
            result.push([item.Item])
        }
    });
    return result.flat()
}

export function allCallableSnippets(snippets: Snippet[] = allSnippetsOf(get(snippetDir))): Snippet[] {
    return snippets.filter(
        (snippet) => snippet.category === "Callable",
    );
}

export function findSnippetIn(directory: Directory<Snippet>, id: number): Snippet | undefined {
    return allSnippetsOf(directory).find(snippet => snippet.id === id);
}

async function moveSnippetToDirectory(
    snippetId: number,
    newParentDirId: number
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets/${snippetId}/move`, {
        method: 'PATCH',
        body: JSON.stringify(newParentDirId),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}

async function uploadSnippet(file: File, parentDirId: number): Promise<Directory<Snippet>> {
    const formData = new FormData();
    formData.append("snippet", file);

    try {
        const response = await fetch(
            `http://${get(networking)}:${networking.port}/snippets_dir/${parentDirId}/upload_snippet`,
            {
                method: "POST",
                body: formData,
            }
        );

        if (response.ok) {
            const result = await response.json();
            TOAST.success(result || `${file.name} uploaded successfully`);
            return fetchSnippets();
        } else {
            const errorText = await response.text();
            TOAST.error(errorText || `HTTP ${response.status}`);
        }
    } catch (error) {
        TOAST.error(`Could not upload ${file.name}: ${error}`);
    }
    return new Promise((_, reject) => reject())
}

async function moveSnippetDirectoryToDirectory(
    directoryId: number,
    newParentDirId: number
): Promise<Directory<Snippet>> {
    await fetch(`http://${get(networking)}:${networking.port}/snippets_dir/${directoryId}/move`, {
        method: 'PATCH',
        body: JSON.stringify(newParentDirId),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchSnippets();
}
