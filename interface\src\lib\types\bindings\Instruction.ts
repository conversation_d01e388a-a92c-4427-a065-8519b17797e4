// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { AddToGroup } from "./AddToGroup";
import type { BlueprintToInstruction } from "./BlueprintToInstruction";
import type { ColorToInstruction } from "./ColorToInstruction";
import type { ConditionalBlock } from "./ConditionalBlock";
import type { DelayByNotesInstruction } from "./DelayByNotesInstruction";
import type { FixtureLoop } from "./FixtureLoop";
import type { InstructionValue } from "./InstructionValue";
import type { LoopStatement } from "./LoopStatement";
import type { PositionIndexOffsetMode } from "./PositionIndexOffsetMode";
import type { PrintStatement } from "./PrintStatement";
import type { QueueMode } from "./QueueMode";
import type { RemoveFromGroup } from "./RemoveFromGroup";
import type { TimecodeToInstruction } from "./TimecodeToInstruction";
import type { UnimplementedChannelSetter } from "./UnimplementedChannelSetter";
import type { VariableSetter } from "./VariableSetter";

export type Instruction = { "instruction": "ExecuteCallableSnippet", "instructionMod": number } | { "instruction": "ColorTo", "instructionMod": ColorToInstruction } | { "instruction": "ColorToRandom", "instructionMod": InstructionValue | null } | { "instruction": "ActivateAllFixtures" } | { "instruction": "ActivateFixtureById", "instructionMod": number } | { "instruction": "ActivateFixtureGroup", "instructionMod": string } | { "instruction": "BlueprintTo", "instructionMod": BlueprintToInstruction } | { "instruction": "TimecodeTo", "instructionMod": TimecodeToInstruction } | { "instruction": "SetBlueprintPositionIndexOffsetMode", "instructionMod": PositionIndexOffsetMode } | { "instruction": "SetSpeedOfBlueprints", "instructionMod": InstructionValue | null } | { "instruction": "SetBlueprintIntensity", "instructionMod": InstructionValue | null } | { "instruction": "ToggleQueueMode", "instructionMod": QueueMode } | { "instruction": "UnimplementedChannelTo", "instructionMod": UnimplementedChannelSetter } | { "instruction": "PanTo", "instructionMod": InstructionValue | null } | { "instruction": "AddToPan", "instructionMod": InstructionValue | null } | { "instruction": "TiltTo", "instructionMod": InstructionValue | null } | { "instruction": "AddToTilt", "instructionMod": InstructionValue | null } | { "instruction": "PositionTo", "instructionMod": number } | { "instruction": "BpmTo", "instructionMod": InstructionValue | null } | { "instruction": "BpmModifierTo", "instructionMod": InstructionValue | null } | { "instruction": "FixtureLoop", "instructionMod": FixtureLoop } | { "instruction": "CreateVariable", "instructionMod": string } | { "instruction": "SetVariable", "instructionMod": VariableSetter } | { "instruction": "AddToGroup", "instructionMod": AddToGroup } | { "instruction": "RemoveFromGroup", "instructionMod": RemoveFromGroup } | { "instruction": "DelayByNotes", "instructionMod": DelayByNotesInstruction } | { "instruction": "DeselectAllFixtures" } | { "instruction": "DimmerTo", "instructionMod": InstructionValue | null } | { "instruction": "ClearBlueprint", "instructionMod": number } | { "instruction": "StartRecording" } | { "instruction": "StopRecording" } | { "instruction": "ClearRecording" } | { "instruction": "Print", "instructionMod": PrintStatement } | { "instruction": "IfStatement", "instructionMod": Array<ConditionalBlock> } | { "instruction": "LoopStatement", "instructionMod": LoopStatement } | { "instruction": "IfQueueAllowsContinue", "instructionMod": Array<Instruction> } | { "instruction": "Nop" };