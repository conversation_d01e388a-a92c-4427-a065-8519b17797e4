// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { FunctionGetter } from "./FunctionGetter";
import type { MathOperator } from "./MathOperator";

export type InstructionValue = { "Number": number } | { "Variable": string } | { "Function": FunctionGetter } | { "Keypoint": number } | { "BlueprintSpeedOfFixture": number } | { "BlueprintIntensityOfFixture": number } | "Value" | "Pressed" | "Released" | "Default" | "Bpm" | "BpmModifier" | "ProgramStartTimestamp" | { "MathOperator": MathOperator } | { "Random": [InstructionValue | null, InstructionValue | null] };