import { PropertyFileDescriptor } from "./types/bindings/PropertyFileDescriptor";
import { TimedPropertyFileDescriptor } from "./types/bindings/TimedPropertyFileDescriptor";
import { get } from "svelte/store";
import { networking } from "./stores/networking";

export function extractPropertyName(property: PropertyFileDescriptor | TimedPropertyFileDescriptor): string {
    if ("PanTiltPositions" in property) {
        return "PanTiltPositions";
    } else if ("ColorPropertyCoordinates" in property) {
        return "ColorTransition";
    } else if ("CallSnippet" in property) {
        return "CallSnippet";
    } else {
        return property.UnimplementedChannel[0];
    }
}

export function createDefaultTimedPropertyBasedOn(
    propertyName: string,
    xOffset: number
): TimedPropertyFileDescriptor {
    if (propertyName === "ColorTransition") {
        return {
            ColorPropertyCoordinates: [
                [
                    { x: 0, color: { red: 255, green: 0, blue: 0 } },
                    { x: 160, color: { red: 0, green: 0, blue: 255 } },
                ],
                xOffset,
                [],
            ],
        }
    } else if (propertyName === "CallSnippet") {
        return {
            CallSnippet: [
                0,
                xOffset,
                [],
            ],
        };
    } else {
        return {
            UnimplementedChannel: [
                propertyName,
                [
                    { x: 0, y: 128 },
                    { x: 160, y: 128 },
                ],
                xOffset,
                [],
            ],
        };
    }
}

export function mapRange(value: number, fromMin: number, fromMax: number, toMin: number, toMax: number): number {
    if (fromMin === fromMax) {
        throw new Error("fromMin and fromMax cannot be the same value.");
    }

    return toMin + ((value - fromMin) * (toMax - toMin)) / (fromMax - fromMin);
}

export function isLightColor(hexColor: string): boolean {
    const hex = hexColor.replace('#', '');

    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);

    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    return luminance > 0.5;
}

export function getThemeTextColors(currentTheme: string) {
    const themeColors = {
        dark: {
            primary: '#FFFFFF',
            complementary: '#000000'
        },
        light: {
            primary: '#000000',
            complementary: '#FFFFFF'
        },
        solar: {
            primary: '#002B36',
            complementary: '#EEE8D5'
        },
        catpuccin: {
            primary: '#FFFFFF',
            complementary: '#303446'
        }
    };

    return themeColors[currentTheme as keyof typeof themeColors] || themeColors.dark;
}


export async function fetchDmxOutput(universe: number = 1): Promise<number[]> {
    try {
        const ip = get(networking);
        if (!ip) {
            return []
        }

        const url = `http://${ip}:${networking.port}/dmxuniverse/${universe}`;
        const response = await fetch(url);

        if (response.ok) {
            return await response.json();
        } else {
            throw new Error('Unable to retreive the currect dmx universe data');
        }
    } catch (error) {
        console.error('Failed to fetch DMX output:', error);
        throw error;
    }
}

export function stringToColor(str: string) {
    function hashString(str: string) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            hash = str.charCodeAt(i) + ((hash << 5) - hash);
            hash = hash & hash;
        }
        return hash;
    }

    function intToRGB(i: number) {
        const c = (i & 0x00ffffff).toString(16).toUpperCase();
        return "00000".substring(0, 6 - c.length) + c;
    }

    if (str) {
        const hash = hashString(str);
        const color = intToRGB(hash);
        return `#${color}`;
    } else {
        return "#FF0000";
    }
}
