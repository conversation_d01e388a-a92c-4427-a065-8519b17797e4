<script>
    import "../app.css";
    import Navbar from "$lib/organisms/navbar.svelte";
    import ToastContainer from "$lib/molecules/ToastContainer.svelte";
    import { networking } from "$lib/stores/networking";
    import { shows } from "$lib/stores/shows";
    import { goto } from "$app/navigation";
    import { page } from "$app/stores";

    let { children } = $props();

    $effect(() => {
        if ($networking) {
            if (
                $shows.length === 0 &&
                $page.url.pathname !== "/shows" &&
                $page.url.pathname !== "/networking"
            ) {
                goto("shows");
            }
        } else if ($page.url.pathname !== "/networking") {
            goto("networking");
        }
    });
</script>

<div class="h-full min-h-screen bg-primary text-primary">
    <Navbar />
    <!-- TODO: Review placement - Does the container belong inside the div, or could it be better suited outside? -->
    <ToastContainer />
    {#if $networking || $page.url.pathname === "/networking"}
        <div class="p-10">
            {@render children()}
        </div>
    {/if}
</div>
