<script lang="ts">
    import {
        blueprints,
        interpolationMethod,
        updateInterpolationMethod,
    } from "$lib/stores/blueprints";
    import { fixtures } from "$lib/stores/fixtures";
    import LabeledDropdown from "$lib/molecules/labeled_dropdown.svelte";
    import Select from "$lib/atoms/select.svelte";
    import Button from "$lib/atoms/button.svelte";
    import type { PropertyFileDescriptor } from "$lib/types/bindings/PropertyFileDescriptor";
    import UnimplementedChannelProperty from "./partials/unimplementedChannelProperty.svelte";
    import PanTiltProperty from "./partials/panTiltProperty.svelte";
    import ColorTransitionProperty from "./partials/colorTransitionProperty.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import Numberinput from "$lib/atoms/numberinput.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import { page } from "$app/stores";
    import {
        allLeafesOf,
        appendSnippetEditingReason,
    } from "$lib/stores/snippets";
    import RequiresUseractionHint from "$lib/atoms/RequiresUseractionHint.svelte";
    import SearchableDropdown from "$lib/molecules/searchable_dropdown.svelte";
    import { extractPropertyName } from "$lib/utils";
    import { untrack } from "svelte";
    import {
        BEAT_INDEX_POINTS,
        VISUAL_BEAT_INDEX_POINT_MODIFIER,
    } from "./partials/constants";

    let selected_blueprint_id: number | undefined = $state();

    let selected_blueprint = $derived.by(() =>
        $blueprints?.find(
            (blueprint) => blueprint.id === selected_blueprint_id,
        ),
    );
    let selected_addable_fixture_id = $state(null);
    let newDropIndex: number | null = $state(null);

    let needsSaving = $state(false);

    const possible_fixture_properties = $derived.by(() => {
        let result = [
            ...new Set([
                "PanTiltPositions",
                "Pan",
                "Tilt",
                "ColorTransition",
                ...$fixtures
                    .map((fixture) => fixture.unimplemented_channels)
                    .flat()
                    .map((unim_channel) => unim_channel.name),
            ]),
        ];

        result = result.filter(
            (property) =>
                !selected_blueprint?.properties.find((blueprint_property) => {
                    return property === extractPropertyName(blueprint_property);
                }),
        );

        result.sort();
        return result;
    });

    let selected_to_add_property: string | null = $state(null);

    let borderPointsLocked: boolean[] = $state([]);
    $effect(() => {
        const blueprintpropertySizedArray = new Array(
            selected_blueprint?.properties.length,
        );
        blueprintpropertySizedArray.fill(false);
        untrack(() => {
            borderPointsLocked = blueprintpropertySizedArray;
        });
    });

    $effect(() => {
        if (selected_blueprint_id !== undefined) {
            needsSaving = false;
        }
    });

    function add_property_to_blueprint(property: string) {
        if (!selected_blueprint || !property || !property.length) return;
        needsSaving = true;
        if (property === "PanTiltPositions") {
            selected_blueprint.properties.push({
                PanTiltPositions: [[], "Linear"],
            });
        } else if (property === "ColorTransition") {
            selected_blueprint.properties.push({
                ColorPropertyCoordinates: [
                    [
                        { x: 0, color: { red: 255, green: 0, blue: 0 } },
                        {
                            x:
                                BEAT_INDEX_POINTS *
                                VISUAL_BEAT_INDEX_POINT_MODIFIER,
                            color: { red: 0, green: 255, blue: 0 },
                        },
                        {
                            x:
                                BEAT_INDEX_POINTS *
                                VISUAL_BEAT_INDEX_POINT_MODIFIER *
                                2,
                            color: { red: 0, green: 0, blue: 255 },
                        },
                    ],
                    "Linear",
                ],
            });
        } else {
            selected_blueprint.properties.push({
                UnimplementedChannel: [
                    property,
                    [
                        { x: 0, y: 128 },
                        {
                            x:
                                BEAT_INDEX_POINTS *
                                VISUAL_BEAT_INDEX_POINT_MODIFIER,
                            y: 128,
                        },
                    ],
                    "Linear",
                ],
            });
        }
        blueprints.patchOne(selected_blueprint).then((_) => {
            needsSaving = false;
        });
    }

    function resetPropertyBorders(property: PropertyFileDescriptor) {
        if (!selected_blueprint || !property) return;
        needsSaving = true;
        let extractedToResetPropertyName = extractPropertyName(property);
        let toResetProperty = selected_blueprint.properties.find(
            (bpProperty) =>
                extractPropertyName(bpProperty) ===
                extractedToResetPropertyName,
        );
        if (toResetProperty && "UnimplementedChannel" in toResetProperty) {
            toResetProperty.UnimplementedChannel[1][0].y = 128;
            toResetProperty.UnimplementedChannel[1].slice(-1)[0].y = 128;
        }
        blueprints.patchOne(selected_blueprint).then((_) => {
            needsSaving = false;
        });
    }

    function removeProperty(property: PropertyFileDescriptor) {
        if (!selected_blueprint || !property) return;
        needsSaving = true;
        let extractedToDeletePropertyName = extractPropertyName(property);
        blueprints
            .patchOne({
                ...selected_blueprint,
                properties: selected_blueprint.properties.filter(
                    (current_property) =>
                        extractPropertyName(current_property) !==
                        extractedToDeletePropertyName,
                ),
            })
            .then((_) => {
                needsSaving = false;
            });
    }

    function add_fixture_to_blueprint(fixture_id: number | null) {
        if (selected_blueprint && fixture_id !== null) {
            needsSaving = true;
            selected_blueprint.registered_fixture_delays.push({
                fixture_id,
                delay_eights: 0,
            });
            blueprints.patchOne(selected_blueprint).then((result) => {
                needsSaving = false;
            });
        }
    }
</script>

<div class="space-x-6 flex w-full">
    <div class="rounded-xl bg-object shadow-lg border border-input/10 grow">
        <div class="p-6">
            <h2
                class="text-lg font-semibold text-primary mb-4 flex items-center"
            >
                <div class="text-accent mr-2">
                    <Icon icon="material-symbols:text-snippet-outline"></Icon>
                </div>
                Blueprint Management
            </h2>

            <div class="grid grid-cols-1 xl:grid-cols-12 gap-6">
                <div class="lg:col-span-6">
                    <div class="space-y-3">
                        <div class="flex flex-col space-y-2">
                            <SearchableDropdown
                                id="blueprintSelect"
                                bind:value={selected_blueprint_id}
                                items={$blueprints}
                            ></SearchableDropdown>
                        </div>

                        {#if selected_blueprint}
                            <div class="flex flex-col space-y-2">
                                <LabeledTextinput
                                    label="Blueprint Name"
                                    bind:value={selected_blueprint.name}
                                    placeholder="Enter blueprint name..."
                                    onchange={() => (needsSaving = true)}
                                ></LabeledTextinput>
                            </div>
                        {/if}
                    </div>
                </div>

                <div class="flex flex-col space-y-2">
                    <Button
                        id="blueprintSave"
                        onclick={() => {
                            if (selected_blueprint && needsSaving) {
                                selected_blueprint.requires_user_action_reason =
                                    null;
                                needsSaving = false;
                                blueprints
                                    .patchOne(selected_blueprint)
                                    .then((_) => {
                                        needsSaving = false;
                                    });
                            }
                        }}
                    >
                        {#if needsSaving}
                            <div
                                class="flex justify-center w-full rounded-lg p-4 text-3xl text-info"
                            >
                                <div class="animate-shake-once">
                                    <Icon icon="material-symbols:save"></Icon>
                                </div>
                            </div>
                        {:else}
                            <div
                                class="flex justify-center rounded-lg p-4 text-3xl text-success"
                            >
                                <Icon icon="material-symbols:save"></Icon>
                                <div class="absolute animate-ping-once">
                                    <Icon icon="material-symbols:save"></Icon>
                                </div>
                            </div>
                        {/if}
                    </Button>

                    <Button
                        id="blueprintCreate"
                        onclick={async () => {
                            await blueprints.createOne();
                            selected_blueprint_id = $blueprints.length + 1;
                        }}
                        grow
                    >
                        <div class="flex items-center justify-center space-x-2">
                            <div class="text-lg">
                                <Icon icon="mdi:add"></Icon>
                            </div>
                            <span class="text-sm font-medium">New</span>
                        </div>
                    </Button>

                    <Button
                        id="blueprintRemove"
                        onclick={() => {
                            if (selected_blueprint) {
                                appendSnippetEditingReason(
                                    (snippet) =>
                                        snippet.instructions
                                            .flatMap((instruction) =>
                                                allLeafesOf(instruction),
                                            )
                                            .some((instruction) => {
                                                if (
                                                    "instruction" in instruction
                                                ) {
                                                    if (
                                                        "BlueprintTo" ===
                                                        instruction.instruction
                                                    ) {
                                                        return (
                                                            instruction
                                                                .instructionMod
                                                                .id ===
                                                            selected_blueprint.id
                                                        );
                                                    }
                                                }
                                                return false;
                                            }),
                                    `deleted blueprint ${selected_blueprint.name}`,
                                );
                                blueprints.deleteOne(selected_blueprint);
                            }
                        }}
                        disabled={selected_blueprint_id === undefined}
                        grow
                    >
                        <div class="flex items-center justify-center space-x-2">
                            <div class="text-lg text-error">
                                <Icon icon="ri:subtract-fill"></Icon>
                            </div>
                            <span class="text-sm font-medium">Delete</span>
                        </div>
                    </Button>
                </div>
            </div>
        </div>
    </div>

    {#if selected_blueprint}
        <div class="rounded-xl bg-object shadow-lg border border-input/10">
            <div class="p-6">
                <h3
                    class="text-lg font-semibold text-primary mb-4 flex items-center"
                >
                    <div class="text-accent mr-2">
                        <Icon icon="mdi:wand"></Icon>
                    </div>
                    Property Management
                </h3>

                <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
                    <div class="lg:col-span-8">
                        <div class="flex flex-col space-y-2">
                            <LabeledDropdown
                                bind:value={selected_to_add_property}
                                label="Add Property to Blueprint"
                            >
                                {#each possible_fixture_properties as property}
                                    <option value={property}>{property}</option>
                                {/each}
                            </LabeledDropdown>
                        </div>
                    </div>

                    <div class="lg:col-span-4">
                        <div class="flex flex-col space-y-2">
                            <div class="mt-6">
                                <Button
                                    id="add-property-button"
                                    onclick={() => {
                                        if (selected_to_add_property !== null) {
                                            add_property_to_blueprint(
                                                selected_to_add_property,
                                            );
                                        }
                                    }}
                                    disabled={selected_to_add_property === null}
                                    grow
                                >
                                    <div
                                        class="flex items-center justify-center space-x-2"
                                    >
                                        <div class="text-lg">
                                            <Icon icon="mdi:add"></Icon>
                                        </div>
                                        <span class="font-medium"
                                            >Add Property</span
                                        >
                                    </div>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}
</div>
{#key $page.url.pathname}
    {#key selected_blueprint_id}
        {#if selected_blueprint && selected_blueprint.properties}
            <div class="mt-4 space-y-4 rounded-lg bg-object p-4">
                {#each selected_blueprint.properties as property, i (extractPropertyName(property) + selected_blueprint_id)}
                    <div id={extractPropertyName(property)}>
                        <div class="flex space-x-4 text-nowrap">
                            <RequiresUseractionHint
                                reason={selected_blueprint.requires_user_action_reason?.includes(
                                    "position",
                                ) && "PanTiltPositions" in property
                                    ? selected_blueprint.requires_user_action_reason
                                    : null}
                            >
                                {#if "PanTiltPositions" in property}
                                    <p class="mt-4">Pan/Tilt Positions</p>
                                {:else if "ColorPropertyCoordinates" in property}
                                    <p class="mt-4">Color Transition</p>
                                {:else if "CallSnippet" in property}
                                    <!-- No snippets can be called inside blueprints -->
                                {:else}
                                    <p class="mt-4">
                                        {property.UnimplementedChannel[0]}
                                    </p>
                                {/if}
                            </RequiresUseractionHint>
                            {#if extractPropertyName(property) !== "PanTiltPositions" && extractPropertyName(property) !== "ColorTransition"}
                                <div class="mt-3">
                                    <Button
                                        id="reset-property-button"
                                        onclick={() => {
                                            resetPropertyBorders(property);
                                        }}
                                        small
                                    >
                                        <div class="text-xl">
                                            <Icon icon="bx:reset"></Icon>
                                        </div>
                                    </Button>
                                </div>
                            {/if}
                            <div class="mt-3">
                                <Button
                                    id="lock-border-button"
                                    onclick={() => {
                                        borderPointsLocked[i] =
                                            !borderPointsLocked[i];
                                    }}
                                    small
                                >
                                    <div class="w-5 h-5">
                                        {#if borderPointsLocked[i]}
                                            <Icon icon="material-symbols:lock"
                                            ></Icon>
                                        {:else}
                                            <Icon
                                                icon="material-symbols:lock-open"
                                            ></Icon>
                                        {/if}
                                    </div>
                                </Button>
                            </div>
                            <div class="mt-2.5">
                                <Select
                                    value={interpolationMethod(property)}
                                    onchange={(newValue) => {
                                        updateInterpolationMethod(
                                            property,
                                            newValue ?? "Linear",
                                        );
                                        needsSaving = true;
                                    }}
                                    disableYPadding
                                >
                                    <option value="Linear">Linear </option>
                                    <option value="Cosine"> Cosine </option>
                                </Select>
                            </div>
                            <div class="mt-3 flex justify-end w-full">
                                <div class="w-fit">
                                    <Button
                                        id="remove-property-button"
                                        onclick={() => {
                                            removeProperty(property);
                                            needsSaving = true;
                                        }}
                                        small
                                    >
                                        <div class="text-xl">
                                            <Icon icon="mdi:garbage-can-empty"
                                            ></Icon>
                                        </div>
                                    </Button>
                                </div>
                            </div>
                        </div>
                        {#if "PanTiltPositions" in property}
                            <PanTiltProperty
                                bind:points={property.PanTiltPositions[0]}
                                onchange={() => (needsSaving = true)}
                            />
                        {:else if "ColorPropertyCoordinates" in property}
                            <ColorTransitionProperty
                                bind:points={
                                    property.ColorPropertyCoordinates[0]
                                }
                                bind:borderPointsLocked={borderPointsLocked[i]}
                                onchange={() => (needsSaving = true)}
                            />
                        {:else if "CallSnippet" in property}
                            <!-- No snippets can be called inside blueprints -->
                        {:else}
                            <UnimplementedChannelProperty
                                bind:points={property.UnimplementedChannel[1]}
                                interpolationMethod={interpolationMethod(
                                    property,
                                ) ?? "Linear"}
                                bind:borderPointsLocked={borderPointsLocked[i]}
                                onchange={() => (needsSaving = true)}
                            />
                        {/if}
                    </div>
                {/each}
            </div>
            <div class="mt-4 flex justify-evenly rounded-lg bg-object p-4">
                <table class="w-1/4">
                    <thead class="text- border-b">
                        <tr>
                            <th scope="col" class="px-6 py-4 text-left text-sm">
                                Name
                            </th>
                            <th scope="col" class="px-6 py-4 text-left text-sm">
                                <p>Delay by eights</p>
                                <p>(A 4/4 beat has eight eights)</p>
                            </th>
                        </tr>
                    </thead>
                    <tbody
                        ondragend={async (e) => {
                            if (newDropIndex !== null) {
                                selected_blueprint.registered_fixture_delays.splice(
                                    newDropIndex,
                                    0,
                                    await JSON.parse(
                                        e.dataTransfer?.getData("text/plain") ??
                                            "",
                                    ),
                                );
                            }
                        }}
                    >
                        {#each selected_blueprint.registered_fixture_delays as fixture (fixture.fixture_id)}
                            <tr>
                                <td class="whitespace-nowrap px-6 text-sm">
                                    {$fixtures.find(
                                        (store_fixture) =>
                                            store_fixture.id ===
                                            fixture.fixture_id,
                                    )?.name}
                                </td>
                                <td class="whitespace-nowrap px-6 text-sm">
                                    <Numberinput
                                        bind:value={fixture.delay_eights}
                                    ></Numberinput>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
                <p class="w-1/4">
                    Only fixtures that have something configured as theyr
                    blueprint delay are enumerated in the list, but this
                    blueprint can be applied to ALL fixtures regardless if they
                    are listed in the table.
                </p>
                <div class="w-1/3">
                    <LabeledDropdown
                        bind:value={selected_addable_fixture_id}
                        label="Addable fixtures"
                    >
                        {#each $fixtures.filter((store_fixture) => !selected_blueprint.registered_fixture_delays
                                    .map((fixture) => fixture.fixture_id)
                                    .includes(store_fixture.id ?? -1)) as fixture}
                            <option value={fixture.id}>{fixture.name}</option>
                        {/each}
                    </LabeledDropdown>
                    <div class="mt-1">
                        <Button
                            id="add-fixture-button"
                            onclick={() =>
                                add_fixture_to_blueprint(
                                    selected_addable_fixture_id,
                                )}
                        >
                            <div class="flex">
                                <div class="mt-1">
                                    <Icon icon="mdi:add"></Icon>
                                </div>
                                <div class="ml-1">fixture</div>
                            </div>
                        </Button>
                    </div>
                </div>
            </div>
        {/if}
    {/key}
{/key}
