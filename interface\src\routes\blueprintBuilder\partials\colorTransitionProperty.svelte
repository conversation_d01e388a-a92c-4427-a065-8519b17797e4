<script lang="ts">
    import P5jssketch from "$lib/molecules/p5jssketch.svelte";
    import { onMount } from "svelte";
    import type { ColorPropertyCoordinate } from "$lib/types/bindings/ColorPropertyCoordinate";
    import { remap } from "@anselan/maprange";
    import Icon from "$lib/atoms/icon.svelte";
    import {
        BEAT_INDEX_POINTS,
        MAX_BEAT_INDEX_POINTS,
        MAX_PROPERTY_BEAT_LENGTH,
        VISUAL_BEAT_INDEX_POINT_MODIFIER,
    } from "./constants";

    interface Point {
        x: number;
        hue: number;
        moving: boolean;
        border: boolean;
    }

    const HEIGHT = 90;

    let rootElement: HTMLElement | undefined = $state();

    let {
        points = $bindable(),
        borderPointsLocked = $bindable(),
        onchange,
    }: {
        points: ColorPropertyCoordinate[];
        borderPointsLocked: boolean;
        onchange?: () => any;
    } = $props();

    let processedPoints: Point[] = $state([]);

    let width = $derived(
        processedPoints && processedPoints.length >= 2
            ? processedPoints[processedPoints.length - 1].x
            : 0,
    );

    $effect(() => {
        if (processedPoints.length)
            points = processedPoints.map((pnt) => {
                return {
                    x: pnt.x * VISUAL_BEAT_INDEX_POINT_MODIFIER,
                    color: rgbFromHue(pnt.hue),
                };
            });
    });

    let scrollLeft = $state(0);
    let clientWidth = $state(0);

    onMount(() => {
        points.forEach((pnt) =>
            processedPoints.push({
                x: pnt.x / VISUAL_BEAT_INDEX_POINT_MODIFIER,
                moving: false,
                border: false,
                hue: rgbToHsv(pnt.color.red, pnt.color.green, pnt.color.blue)
                    .hue,
            }),
        );
        processedPoints[0].border = true;
        processedPoints[processedPoints.length - 1].border = true;

        scrollLeft = rootElement?.scrollLeft ?? 0;
        clientWidth = rootElement?.clientWidth ?? 0;
    });

    let dragging = $state(false);

    function move_mouse(movementX: number) {
        if (dragging) {
            let newWidth = (processedPoints[processedPoints.length - 1].x +=
                movementX);
            if (newWidth > MAX_BEAT_INDEX_POINTS) {
                newWidth = MAX_BEAT_INDEX_POINTS;
            }
            processedPoints[processedPoints.length - 1].x = newWidth;
        }
    }

    function syncBorderPointsHue() {
        if (processedPoints.length >= 2) {
            const firstPoint = processedPoints[0];
            const lastPoint = processedPoints[processedPoints.length - 1];
            if (firstPoint.border && lastPoint.border) {
                // Use the average hue value of both border points
                const avgHue = (firstPoint.hue + lastPoint.hue) / 2;
                firstPoint.hue = avgHue;
                lastPoint.hue = avgHue;
            }
            if (onchange) onchange();
        }
    }

    function update_width(new_width: number) {
        processedPoints[processedPoints.length - 1].x = new_width;
        if (onchange) onchange();
    }

    let interaction_type = $state(0);
    const sketch = (p5: any) => {
        p5.setup = () => {};

        p5.draw = () => {
            p5.createCanvas(width, HEIGHT);
            p5.background(0);

            p5.strokeWeight(2);
            for (let i = 0; i < width - 1; i++) {
                let prev: { hue: number; x: number };
                let filteredProcessedPoint = processedPoints.filter(
                    (point) => point.x < i,
                );
                if (filteredProcessedPoint.length) {
                    prev =
                        filteredProcessedPoint[
                            filteredProcessedPoint.length - 1
                        ];
                } else {
                    prev = { hue: 0, x: 0 };
                }
                let next: { hue: number; x: number };
                filteredProcessedPoint = processedPoints.filter(
                    (point) => point.x > i,
                );
                if (filteredProcessedPoint.length) {
                    next = filteredProcessedPoint[0];
                } else {
                    next = { hue: 0, x: width };
                }

                let interpolatedHue;
                try {
                    interpolatedHue = Math.floor(
                        remap(i, [prev.x, next.x], [prev.hue, next.hue]),
                    );
                } catch {
                    interpolatedHue = prev.hue;
                }
                p5.stroke(hexColorFromObject(rgbFromHue(interpolatedHue)));
                p5.line(i, 0, i, HEIGHT);
            }
            interaction_type = 0;
            processedPoints.forEach((pnt) => {
                if (p5.mouseX > pnt.x - 10 && p5.mouseX < pnt.x + 10) {
                    interaction_type = 1;
                }
            });
            p5.cursor(interaction_type === 0 ? p5.ARROW : p5.MOVE);

            processedPoints.forEach((pnt) => {
                if (
                    pnt.moving &&
                    !pnt.border &&
                    p5.mouseY < HEIGHT &&
                    p5.mouseY > 0 &&
                    p5.mouseX < width &&
                    p5.mouseX > 0
                ) {
                    pnt.x = p5.mouseX;
                }
            });

            processedPoints.forEach((pnt) =>
                pnt.x > width ? (pnt.x = width) : null,
            );

            let point = processedPoints.find((point) => point.moving);
            if (point) {
                if (p5.keyIsDown(171 /* + */)) {
                    point.hue += 1;
                } else if (p5.keyIsDown(173 /* - */)) {
                    point.hue -= 1;
                } else if (p5.keyIsDown(p5.UP_ARROW)) {
                    point.hue += 10;
                } else if (p5.keyIsDown(p5.DOWN_ARROW)) {
                    point.hue -= 10;
                }
                if (point.hue < 0) point.hue = 0;
                if (point.hue > 359) point.hue = 359;
            }
            if (borderPointsLocked) {
                syncBorderPointsHue();
            }
            p5.stroke(0);
            processedPoints.forEach((pnt) => {
                p5.strokeWeight(2);
                p5.line(pnt.x, 0, pnt.x, HEIGHT);
                p5.strokeWeight(6);
                p5.fill(hexColorFromObject(rgbFromHue(pnt.hue)));

                p5.circle(pnt.x, HEIGHT / 2, 15);
            });
        };

        p5.mousePressed = () => {
            if (
                p5.mouseY < HEIGHT &&
                p5.mouseY > 0 &&
                p5.mouseX < width &&
                p5.mouseX > 0
            )
                if (interaction_type === 0) {
                    processedPoints.push({
                        x: p5.mouseX,
                        hue: p5.mouseY,
                        moving: false,
                        border: false,
                    });
                } else {
                    processedPoints.forEach((pnt) => (pnt.moving = false));
                    processedPoints.sort((a, b) =>
                        p5.dist(a.x, 0, p5.mouseX, 0) >
                        p5.dist(b.x, 0, p5.mouseX, 0)
                            ? 1
                            : -1,
                    )[0].moving = true;
                }
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
        };

        p5.mouseReleased = () => {
            processedPoints.forEach((pnt) => (pnt.moving = false));
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
            if (borderPointsLocked) {
                syncBorderPointsHue();
            }
            if (
                p5.mouseY < HEIGHT &&
                p5.mouseY > 0 &&
                p5.mouseX < width &&
                p5.mouseX > 0 &&
                onchange
            )
                onchange();
        };

        p5.mouseDragged = () => {
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
            if (borderPointsLocked) {
                syncBorderPointsHue();
            }
        };

        p5.mouseWheel = (e: WheelEvent) => {
            if (interaction_type === 1) {
                let point = processedPoints.find((point) => point.moving);
                if (!point) return;
                point.hue += e.deltaY / 10;
                if (point.hue < 0) point.hue = 0;
                if (point.hue > 360) point.hue = 360;

                if (borderPointsLocked && point.border) {
                    syncBorderPointsHue();
                }
            }
        };

        p5.keyPressed = (e: KeyboardEvent) => {
            if (e.code === "Delete" && interaction_type === 1) {
                processedPoints = processedPoints.filter(
                    (point) => !point.moving,
                );
            }
        };

        processedPoints = processedPoints;
    };

    function hexColorFromObject(objColor: {
        red: number;
        green: number;
        blue: number;
    }) {
        let red = objColor.red.toString(16);
        if (red.length === 1) {
            red = `0${red}`;
        }
        let green = objColor.green.toString(16);
        if (green.length === 1) {
            green = `0${green}`;
        }
        let blue = objColor.blue.toString(16);
        if (blue.length === 1) {
            blue = `0${blue}`;
        }
        return `#${red}${green}${blue}`;
    }

    function rgbFromHue(h: number) {
        if (h >= 0 && h < 360) {
            let s = 1;
            let v = 1;
            // @ts-ignore
            let f = (n, k = (n + h / 60) % 6) =>
                v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);
            return {
                red: Math.floor(f(5) * 255),
                green: Math.floor(f(3) * 255),
                blue: Math.floor(f(1) * 255),
            };
        }
        return { red: 0, green: 0, blue: 0 };
    }
    function rgbToHsv(
        r: number,
        g: number,
        b: number,
    ): { hue: number; saturation: number; value: number } {
        r = r / 255;
        g = g / 255;
        b = b / 255;

        let max = Math.max(Math.max(r, g), b);
        let min = Math.min(Math.min(r, g), b);
        let delta = max - min;

        let hue;
        if (delta == 0) {
            hue = 0;
        } else if (max == r) {
            hue = (60.0 * ((g - b) / delta)) % 6.0;
        } else if (max == g) {
            hue = 60.0 * ((b - r) / delta + 2.0);
        } else {
            hue = 60.0 * ((r - g) / delta + 4.0);
        }

        let saturation = max === 0 ? 0 : delta / max;

        let value = max;

        return { hue, saturation, value };
    }
</script>

<svelte:window
    onmousemove={(e) => move_mouse(e.movementX)}
    onmouseup={() => (dragging = false)}
/>

<div
    class="overflow-scroll pb-4"
    bind:this={rootElement}
    onscroll={(e) => {
        // @ts-ignore -> for some reason ts thinks, that there is no outerWidth
        clientWidth = e.target?.clientWidth ? e.target.clientWidth : 0;
        // @ts-ignore -> for some reason ts thinks, that there is no scrollLeft
        scrollLeft = e.target?.scrollLeft ? e.target.scrollLeft : 0;
    }}
>
    <div class="flex justify-start">
        {#each { length: MAX_PROPERTY_BEAT_LENGTH } as _, i}
            <button
                class="h-6 odd:bg-blue-800 even:bg-green-800"
                style:min-width={BEAT_INDEX_POINTS + "px"}
                onclick={() => update_width(BEAT_INDEX_POINTS * (i + 1))}
                aria-label={`property-length-${i}`}
            ></button>
        {/each}
    </div>
    <div class="flex">
        <div class="mt-6">
            <P5jssketch {sketch} />
        </div>
        <button
            class="ml-2 cursor-col-resize"
            style:margin-top={`${HEIGHT / 2 - 23}px`}
            onmousedown={() => (dragging = true)}
            onmouseup={() => {
                if (onchange) onchange();
            }}
        >
            <p>&lt;-&gt;</p>
        </button>
    </div>
    {#if rootElement && (scrollLeft ?? 0) > 0}
        <div class="animate-bounce-left absolute ml-4 text-xl">
            <Icon icon="mdi:arrow-left"></Icon>
        </div>
    {/if}
    {#if rootElement && processedPoints.slice(-1)[0].x > (clientWidth ?? 0) + (scrollLeft ?? 0)}
        <div
            class="animate-bounce-right absolute text-xl"
            style="margin-left: {clientWidth - 30}px"
        >
            <Icon icon="mdi:arrow-right"></Icon>
        </div>
    {/if}
    <div
        class="mt-2 w-fit text-xs italic"
        style:min-width={BEAT_INDEX_POINTS + "px"}
    >
        To change colors, just drag the line and adjust with (`+` and `-`) or
        (`↑` and `↓`) or the mousewheel
    </div>
</div>
