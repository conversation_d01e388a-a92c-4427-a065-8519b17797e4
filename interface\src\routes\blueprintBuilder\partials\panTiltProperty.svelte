<script lang="ts">
    import P5jssketch from "$lib/molecules/p5jssketch.svelte";
    import { onMount } from "svelte";
    import type { PanTiltPositionPropertyCoordinate } from "$lib/types/bindings/PanTiltPositionPropertyCoordinate";
    import LabeledDropdown from "$lib/molecules/labeled_dropdown.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import {
        BEAT_INDEX_POINTS,
        MAX_BEAT_INDEX_POINTS,
        MAX_PROPERTY_BEAT_LENGTH,
        VISUAL_BEAT_INDEX_POINT_MODIFIER,
    } from "./constants";
    import { positions } from "$lib/stores/positions";
    import type { ComposedPanTiltPosition } from "$lib/types/bindings/ComposedPanTiltPosition";

    interface Point {
        x: number;
        position_id: number;
        position_name: string | null;
        moving: boolean;
        border: boolean;
    }

    const HEIGHT = 80;

    let rootElement: HTMLElement | undefined = $state();

    let {
        points = $bindable(),
        onchange,
    }: {
        points: PanTiltPositionPropertyCoordinate[];
        onchange?: () => any;
    } = $props();

    let processedPoints: Point[] = $state([]);
    let nextAddedPosition_id: number | null = $state(null);
    let nextAddedPosition: ComposedPanTiltPosition | null = $derived.by(() => {
        if (nextAddedPosition_id) {
            return (
                $positions.find(
                    (position) => position.id === nextAddedPosition_id,
                ) ?? null
            );
        } else {
            return null;
        }
    });

    let width = $derived(
        processedPoints.length >= 2
            ? processedPoints[processedPoints.length - 1].x
            : MAX_BEAT_INDEX_POINTS,
    );

    $effect(() => {
        if (processedPoints.length) {
            points = processedPoints.map((pnt) => {
                return {
                    x: pnt.x * VISUAL_BEAT_INDEX_POINT_MODIFIER,
                    position_id: pnt.position_id,
                    position_name: pnt.position_name ?? "",
                };
            });
            if (processedPoints.length >= 2) {
                processedPoints[processedPoints.length - 1].border = true;
                for (let i = 1; i < processedPoints.length - 1; ++i) {
                    processedPoints[i].border = false;
                }
            }
        }
    });

    let scrollLeft = $state(0);
    let clientWidth = $state(0);

    onMount(() => {
        points.forEach((pnt) =>
            processedPoints.push({
                x: pnt.x / VISUAL_BEAT_INDEX_POINT_MODIFIER,
                position_id: pnt.position_id,
                moving: false,
                border: false,
                position_name: pnt.position_name,
            }),
        );

        scrollLeft = rootElement?.scrollLeft ?? 0;
        clientWidth = rootElement?.clientWidth ?? 0;
    });

    function update_width(new_width: number) {
        if (onchange) onchange();
        processedPoints[processedPoints.length - 1].x = new_width;
    }

    let dragging = $state(false);
    function move_mouse(movementX: number) {
        if (dragging) {
            let newWidth = (processedPoints[processedPoints.length - 1].x +=
                movementX);
            if (newWidth > MAX_BEAT_INDEX_POINTS) {
                newWidth = MAX_BEAT_INDEX_POINTS;
            }
            processedPoints[processedPoints.length - 1].x = newWidth;
        }
    }

    let interaction_type = $state(0);
    const sketch = (p5: any) => {
        p5.setup = () => {};

        p5.draw = () => {
            p5.createCanvas(width, HEIGHT);
            p5.background(220);

            p5.strokeWeight(1);
            processedPoints.forEach((pnt) => {
                p5.line(pnt.x, 0, pnt.x, HEIGHT);
                let textPositionX = pnt.x + 2;
                if (textPositionX > width / 2) {
                    textPositionX -= (pnt.position_name?.length ?? 0) * 8;
                }
                p5.text(pnt.position_name ?? "", textPositionX, HEIGHT / 2);
            });
            interaction_type = 0;
            processedPoints.forEach((pnt) => {
                if (p5.mouseX > pnt.x - 10 && p5.mouseX < pnt.x + 10) {
                    interaction_type = 1;
                }
            });
            p5.cursor(interaction_type === 0 ? p5.ARROW : p5.MOVE);

            processedPoints.forEach((pnt) => {
                if (
                    pnt.moving &&
                    !pnt.border &&
                    p5.mouseY < HEIGHT &&
                    p5.mouseY > 0 &&
                    p5.mouseX < width &&
                    p5.mouseX > 0
                ) {
                    pnt.x = p5.mouseX;
                }
            });

            processedPoints.forEach((pnt) =>
                pnt.x > width ? (pnt.x = width) : null,
            );
        };

        p5.mousePressed = () => {
            if (
                p5.mouseY < HEIGHT &&
                p5.mouseY > 0 &&
                p5.mouseX < width &&
                p5.mouseX > 0
            )
                if (interaction_type === 0) {
                    if (nextAddedPosition) {
                        processedPoints.push({
                            x: p5.mouseX,
                            moving: false,
                            border: false,
                            position_name: nextAddedPosition.name,
                            position_id: nextAddedPosition.id,
                        });
                        nextAddedPosition_id = null;
                    }
                } else {
                    processedPoints.forEach((pnt) => (pnt.moving = false));
                    processedPoints.sort((a, b) =>
                        p5.dist(a.x, 0, p5.mouseX, 0) >
                        p5.dist(b.x, 0, p5.mouseX, 0)
                            ? 1
                            : -1,
                    )[0].moving = true;
                }
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
        };

        p5.mouseReleased = () => {
            processedPoints.forEach((pnt) => (pnt.moving = false));
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
            if (
                p5.mouseY < HEIGHT &&
                p5.mouseY > 0 &&
                p5.mouseX < width &&
                p5.mouseX > 0 &&
                onchange
            )
                onchange();
        };

        p5.mouseDragged = () => {
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
        };

        p5.keyPressed = (e: KeyboardEvent) => {
            if (e.code === "Delete" && interaction_type === 1) {
                processedPoints = processedPoints.filter(
                    (point) => !point.moving,
                );
            }
        };

        processedPoints = processedPoints;
    };
</script>

<svelte:window
    onmousemove={(e) => move_mouse(e.movementX)}
    onmouseup={() => (dragging = false)}
/>

<div
    class="overflow-scroll pb-4"
    bind:this={rootElement}
    onscroll={(e) => {
        // @ts-ignore -> for some reason ts thinks, that there is no outerWidth
        clientWidth = e.target?.clientWidth ? e.target.clientWidth : 0;
        // @ts-ignore -> for some reason ts thinks, that there is no scrollLeft
        scrollLeft = e.target?.scrollLeft ? e.target.scrollLeft : 0;
    }}
>
    <div class="flex justify-start">
        {#each { length: MAX_PROPERTY_BEAT_LENGTH } as _, i}
            <button
                class="h-6 odd:bg-blue-800 even:bg-green-800"
                style:min-width={BEAT_INDEX_POINTS + "px"}
                onclick={() => update_width(BEAT_INDEX_POINTS * (i + 1))}
                aria-label={`property-length-${i}`}
            ></button>
        {/each}
    </div>
    <div class="flex">
        <div class="mt-6">
            <P5jssketch {sketch} />
        </div>
        {#if processedPoints.length === 0}
            <p class="text-xs">Not resizeable (No positions present)</p>
        {:else if processedPoints.length === 1}
            <p class="text-xs">Not resizeable (Only one position present)</p>
        {:else}
            <button
                class="ml-2 cursor-col-resize"
                style:margin-top={`${HEIGHT / 3}px`}
                onmousedown={() => (dragging = true)}
                onmouseup={() => {
                    if (onchange) onchange();
                }}
            >
                <p>&lt;-&gt;</p>
            </button>
        {/if}
    </div>
    {#if rootElement && (scrollLeft ?? 0) > 0}
        <div class="animate-bounce-left absolute ml-4 text-xl">
            <Icon icon="mdi:arrow-left"></Icon>
        </div>
    {/if}
    {#if rootElement && processedPoints.slice(-1)[0].x > (clientWidth ?? 0) + (scrollLeft ?? 0)}
        <div
            class="animate-bounce-right absolute text-xl"
            style="margin-left: {clientWidth - 30}px"
        >
            <Icon icon="mdi:arrow-right"></Icon>
        </div>
    {/if}
</div>
<div
    class="mt-2 w-fit text-sm italic"
    style:min-width={BEAT_INDEX_POINTS + "px"}
>
    <LabeledDropdown bind:value={nextAddedPosition_id} label="Add position">
        {#each $positions as position}
            <option value={position.id}>{position.name}</option>
        {/each}
    </LabeledDropdown>
</div>
