<script lang="ts">
    import P5jssketch from "$lib/molecules/p5jssketch.svelte";
    import { onMount } from "svelte";
    import { remap } from "@anselan/maprange";
    import type { UnimplementedChannelPropertyCoordinate } from "$lib/types/bindings/UnimplementedChannelPropertyCoordinate";
    import Icon from "$lib/atoms/icon.svelte";
    import {
        BEAT_INDEX_POINTS,
        MAX_BEAT_INDEX_POINTS,
        MAX_PROPERTY_BEAT_LENGTH,
        VISUAL_BEAT_INDEX_POINT_MODIFIER,
    } from "./constants";

    let rootElement: HTMLElement | undefined = $state();

    interface Point {
        x: number;
        y: number;
        moving: boolean;
        border: boolean;
    }

    interface InterpolationPoint {
        x: number;
        y: number;
    }

    const HEIGHT = 128;
    const GUIDE_LINE_HEIGHTS = [0, 0.25, 0.5, 0.75, 1.0];

    let {
        points = $bindable([]),
        onchange,
        interpolationMethod = $bindable(),
        borderPointsLocked = $bindable(),
    }: {
        points: UnimplementedChannelPropertyCoordinate[];
        onchange?: () => any;
        interpolationMethod: string;
        borderPointsLocked: boolean;
    } = $props();

    let processedPoints: Point[] = $state([]);
    $effect(() => {
        points ? setTimeout(() => replaceProcessedPoints(points)) : null;
    });

    function replaceProcessedPoints(
        points: UnimplementedChannelPropertyCoordinate[],
    ) {
        let result_points: Point[] = [];
        points.forEach((pnt) => {
            result_points.push({
                x: pnt.x / VISUAL_BEAT_INDEX_POINT_MODIFIER,
                y: pnt.y,
                moving: false,
                border: false,
            });
        });
        if (result_points.length) {
            result_points[0].border = true;
            result_points[result_points.length - 1].border = true;
            result_points.forEach(
                (pnt) => (pnt.y = remap(pnt.y, [0, 255], [HEIGHT, 0])),
            );
        }

        result_points = result_points.sort((a, b) => (a.x > b.x ? 1 : -1));

        processedPoints = result_points;

        computeInterpolation();
    }

    let width = $derived(
        processedPoints.length > 1
            ? processedPoints[processedPoints.length - 1].x -
                  processedPoints[0].x
            : BEAT_INDEX_POINTS,
    );

    function update_width(new_width: number) {
        processedPoints[processedPoints.length - 1].x = new_width;
        computeInterpolation();
        if (onchange) onchange();
    }

    let dragging = $state(false);

    function move_mouse(movementX: number) {
        if (dragging) {
            let newWidth = (processedPoints[processedPoints.length - 1].x +=
                movementX);
            if (newWidth > MAX_BEAT_INDEX_POINTS) {
                newWidth = MAX_BEAT_INDEX_POINTS;
            }
            processedPoints[processedPoints.length - 1].x = newWidth;
            computeInterpolation();
        }
    }

    function syncBorderPointsY() {
        if (processedPoints.length >= 2) {
            const firstPoint = processedPoints[0];
            const lastPoint = processedPoints[processedPoints.length - 1];
            if (firstPoint.border && lastPoint.border) {
                const avgY = (firstPoint.y + lastPoint.y) / 2;
                firstPoint.y = avgY;
                lastPoint.y = avgY;
            }
        }
    }

    onMount(() => {
        points.forEach((pnt) => {
            processedPoints.push({
                x: pnt.x / VISUAL_BEAT_INDEX_POINT_MODIFIER,
                y: pnt.y,
                moving: false,
                border: false,
            });
        });
        if (processedPoints.length) {
            processedPoints[0].border = true;
            processedPoints[processedPoints.length - 1].border = true;
            processedPoints.forEach(
                (pnt) => (pnt.y = remap(pnt.y, [0, 255], [HEIGHT, 0])),
            );
        }

        sortProcessedPoints();

        computeInterpolation();
    });

    let interpolatedPoints: InterpolationPoint[] = $state([]);
    let interaction_type = $state(0);
    let savedSyncBorderPointsY = $state(false);
    let ctrlPressed = $state(false);

    function snapToGuideLine(y: number): number {
        if (!ctrlPressed) return y;

        let minDistance = Infinity;
        let snappedY = y;

        GUIDE_LINE_HEIGHTS.forEach((guidePercent) => {
            const guideY = HEIGHT * (1 - guidePercent);
            const distance = Math.abs(y - guideY);
            if (distance < minDistance) {
                minDistance = distance;
                snappedY = guideY;
            }
        });

        return snappedY;
    }
    const sketch = (p5: any) => {
        p5.setup = () => {};

        p5.draw = () => {
            p5.createCanvas(width, HEIGHT);
            p5.background(220);

            p5.stroke(180);
            if (ctrlPressed) {
                p5.strokeWeight(2);
            } else {
                p5.strokeWeight(0.1);
            }
            GUIDE_LINE_HEIGHTS.forEach((guidePercent) => {
                const guideY = HEIGHT * (1 - guidePercent);
                p5.line(0, guideY, width, guideY);
            });

            p5.stroke(0);
            p5.strokeWeight(5);
            processedPoints.forEach((pnt) => p5.point(pnt.x, pnt.y));
            p5.strokeWeight(1);
            if (interpolatedPoints.length > 2) {
                for (let i = 1; i < interpolatedPoints.length; i++) {
                    p5.line(
                        interpolatedPoints[i - 1].x,
                        interpolatedPoints[i - 1].y,
                        interpolatedPoints[i].x,
                        interpolatedPoints[i].y,
                    );
                }
            } else if (interpolatedPoints.length === 2) {
                p5.line(
                    interpolatedPoints[0].x,
                    interpolatedPoints[0].y,
                    interpolatedPoints[1].x,
                    interpolatedPoints[1].y,
                );
            } else if (interpolatedPoints.length === 1) {
                p5.line(
                    0,
                    interpolatedPoints[0].y,
                    width,
                    interpolatedPoints[0].y,
                );
            } else {
                p5.text("No points found", width / 5, HEIGHT / 2);
            }
            interaction_type = 0;
            processedPoints.forEach((pnt) => {
                if (
                    p5.mouseX > pnt.x - 10 &&
                    p5.mouseX < pnt.x + 10 &&
                    p5.mouseY > pnt.y - 10 &&
                    p5.mouseY < pnt.y + 10
                ) {
                    interaction_type = 1;
                }
            });
            p5.cursor(interaction_type === 0 ? p5.ARROW : p5.MOVE);

            processedPoints.forEach((pnt) => {
                if (pnt.moving && p5.mouseY < HEIGHT && p5.mouseY > 0) {
                    pnt.y = snapToGuideLine(p5.mouseY);
                    if (!pnt.border && p5.mouseX < width && p5.mouseX > 0) {
                        pnt.x = p5.mouseX;
                    }
                }
            });

            if (borderPointsLocked) {
                syncBorderPointsY();
                if (borderPointsLocked !== savedSyncBorderPointsY) {
                    computeInterpolation();
                }
            }
            savedSyncBorderPointsY = borderPointsLocked;

            if (interaction_type === 1) {
                computeInterpolation();
            }

            if (processedPoints.length) {
                points = processedPoints.map((pnt) => {
                    return {
                        x: pnt.x * VISUAL_BEAT_INDEX_POINT_MODIFIER,
                        y: remap(pnt.y, [HEIGHT, 0], [0, 255]),
                    };
                });
            }
        };

        p5.mousePressed = () => {
            if (
                p5.mouseY < HEIGHT &&
                p5.mouseY > 0 &&
                p5.mouseX < width &&
                p5.mouseX > 0
            )
                if (interaction_type === 0) {
                    processedPoints.push({
                        x: p5.mouseX,
                        y: p5.mouseY,
                        moving: false,
                        border: false,
                    });
                } else {
                    processedPoints.forEach((pnt) => (pnt.moving = false));
                    processedPoints.sort((a, b) =>
                        p5.dist(a.x, a.y, p5.mouseX, p5.mouseY) >
                        p5.dist(b.x, b.y, p5.mouseX, p5.mouseY)
                            ? 1
                            : -1,
                    )[0].moving = true;
                }
            sortProcessedPoints();
            computeInterpolation();
        };

        p5.mouseReleased = () => {
            processedPoints.forEach((pnt) => (pnt.moving = false));
            sortProcessedPoints();
            if (borderPointsLocked) {
                syncBorderPointsY();
            }
            computeInterpolation();
            if (
                p5.mouseY < HEIGHT &&
                p5.mouseY > 0 &&
                p5.mouseX < width &&
                p5.mouseX > 0 &&
                onchange
            )
                onchange();
        };

        p5.mouseDragged = () => {
            sortProcessedPoints();
            if (borderPointsLocked) {
                syncBorderPointsY();
            }
            computeInterpolation();
        };

        p5.keyPressed = (e: KeyboardEvent) => {
            if (e.code === "ControlLeft" || e.code === "ControlRight") {
                ctrlPressed = true;
            }
            if (e.code === "Delete" && interaction_type === 1) {
                processedPoints = processedPoints.filter(
                    (point) => !point.moving,
                );
            }
        };

        p5.keyReleased = (e: KeyboardEvent) => {
            if (e.code === "ControlLeft" || e.code === "ControlRight") {
                ctrlPressed = false;
            }
        };
        processedPoints = processedPoints;
    };
    function sortProcessedPoints() {
        processedPoints = processedPoints.sort((a, b) => (a.x > b.x ? 1 : -1));
    }
    function computeInterpolation() {
        interpolatedPoints = [];
        if (interpolationMethod === "Cosine") {
            for (let j = 0; j < processedPoints.length - 1; j++) {
                for (
                    let i =
                        processedPoints[j].x < processedPoints[j + 1].x
                            ? processedPoints[j].x
                            : processedPoints[j + 1].x;
                    i <
                    Math.abs(processedPoints[j + 1].x - processedPoints[j].x) +
                        (processedPoints[j].x < processedPoints[j + 1].x
                            ? processedPoints[j].x
                            : processedPoints[j + 1].x);
                    i += 3
                ) {
                    interpolatedPoints.push({
                        x: i,
                        y:
                            (Math.cos(
                                ((i - processedPoints[j].x) /
                                    (processedPoints[j + 1].x -
                                        processedPoints[j].x)) *
                                    Math.PI,
                            ) -
                                1) *
                                0.5 *
                                (processedPoints[j].y -
                                    processedPoints[j + 1].y) +
                            processedPoints[j].y,
                    });
                }
            }
        } else if (interpolationMethod === "Linear") {
            interpolatedPoints = processedPoints.map((processedPoint) => {
                return {
                    x: processedPoint.x,
                    y: processedPoint.y,
                };
            });
        }
    }

    let scrollLeft = $state(0);
    let clientWidth = $state(0);
    onMount(() => {
        scrollLeft = rootElement?.scrollLeft ?? 0;
        clientWidth = rootElement?.clientWidth ?? 0;
    });
</script>

<svelte:window
    onmousemove={(e) => move_mouse(e.movementX)}
    onmouseup={() => (dragging = false)}
    onkeydown={(e) => {
        if (e.code === "ControlLeft" || e.code === "ControlRight") {
            ctrlPressed = true;
        }
    }}
    onkeyup={(e) => {
        if (e.code === "ControlLeft" || e.code === "ControlRight") {
            ctrlPressed = false;
        }
    }}
/>

<div
    class="overflow-scroll pb-4"
    bind:this={rootElement}
    onscroll={(e) => {
        // @ts-ignore -> for some reason ts thinks, that there is no outerWidth
        clientWidth = e.target?.clientWidth ? e.target.clientWidth : 0;
        // @ts-ignore -> for some reason ts thinks, that there is no scrollLeft
        scrollLeft = e.target?.scrollLeft ? e.target.scrollLeft : 0;
    }}
>
    <div class="flex justify-start">
        {#each { length: MAX_PROPERTY_BEAT_LENGTH } as _, i}
            <button
                class="h-6 odd:bg-blue-800 even:bg-green-800"
                style={`min-width: ${BEAT_INDEX_POINTS}px`}
                onclick={() => update_width(BEAT_INDEX_POINTS * (i + 1))}
                aria-label={`property-length-${i}`}
            ></button>
        {/each}
    </div>
    <div class="flex">
        <P5jssketch {sketch} />
        <button
            class="ml-2 cursor-col-resize"
            style:margin-top={`${HEIGHT / 2 - 38}px`}
            onmousedown={() => (dragging = true)}
            onmouseup={() => {
                if (onchange) onchange();
            }}
        >
            <p>&lt;-&gt;</p>
        </button>
    </div>
    {#if rootElement && (scrollLeft ?? 0) > 0}
        <div class="absolute ml-4 animate-bounce-left text-xl">
            <Icon icon="mdi:arrow-left"></Icon>
        </div>
    {/if}
    {#if rootElement && processedPoints.slice(-1)[0].x > (clientWidth ?? 0) + (scrollLeft ?? 0)}
        <div
            class="absolute animate-bounce-right text-xl"
            style="margin-left: {clientWidth - 30}px"
        >
            <Icon icon="mdi:arrow-right"></Icon>
        </div>
    {/if}
</div>
