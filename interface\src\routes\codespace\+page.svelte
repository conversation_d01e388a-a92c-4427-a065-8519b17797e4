<script lang="ts" module>
    export interface BlockInstanciator {
        ExecuteCallableSnippet: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ActivateAllFixtures: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ActivateFixtureById: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ActivateFixtureGroup: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        BpmTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        BpmModifierTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        DelayBy: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        DelayByNotes: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        UnimplementedChannelTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ResetUnimplementedChannel: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        PositionTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        IfStatement: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        LoopStatement: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        FixtureLoop: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        AddToGroup: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        RemoveFromGroup: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        IfQueueAllowsContinue: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        InputChanged: (
            workspace: Blockly.Workspace,
            childs: Instruction[],
        ) => Blockly.Block | undefined;
        Startup: (
            workspace: Blockly.Workspace,
            childs: Instruction[],
        ) => Blockly.Block | undefined;
        Watcher: (
            workspace: Blockly.Workspace,
            childs: Instruction[],
        ) => Blockly.Block | undefined;
        Callable: (
            workspace: Blockly.Workspace,
            childs: Instruction[],
        ) => Blockly.Block | undefined;
        CreateVariable: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        SetVariable: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ColorTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ColorToRandom: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        BlueprintTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ToggleAdvanceBlueprintPositionIndex: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        SetBlueprintPositionIndexOffsetMode: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        SetSpeedOfBlueprints: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        SetBlueprintIntensity: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ToggleQueueMode: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        PanTiltSpeedTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        PanTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        TiltTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        AddToPan: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        AddToTilt: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        DimmerTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ClearBlueprint: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        StartRecording: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        StopRecording: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        ClearRecording: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        Print: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        // This is no blockly block but used in the `host`
        DeselectAllFixtures: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        TimecodeTo: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
        Nop: (
            workspace: Blockly.Workspace,
            instruction: Instruction,
        ) => Blockly.Block | undefined;
    }
    export const CATEGORY_COLOR = {
        TEST: "#000000",
        TRIGGER: "#8A2F91",
        CONTROLFLOW: "#2B8C23",
        TIME: "#AA1721",
        COMMANDS: "#0B717A",
        VALUES: "#7D8737",
        VARIABLES: "#877437",
        FIXTURES: "#2D049E",
    };

    export function getLastConnectionOfConnection(
        connection: Blockly.Connection | undefined | null,
    ) {
        while (connection?.targetBlock()) {
            const target = connection.targetBlock();
            connection = target?.nextConnection;
        }
        return connection;
    }
</script>

<script lang="ts">
    import * as Blockly from "blockly/core";
    import { javascriptGenerator } from "blockly/javascript";
    import BlocklyComponent, {
        generateWorkspaceSnapshot,
        restoreBackpack,
    } from "./partials/blocklyComponent.svelte";
    import * as backpackPkg from "@blockly/workspace-backpack";
    import type { Instruction } from "$lib/types/bindings/Instruction";
    import { generateCode } from "./partials/blocklyComponent.svelte";
    import { fixtures } from "$lib/stores/fixtures";
    import {
        snippetDir,
        findSnippetIn,
        allSnippetsOf,
    } from "$lib/stores/snippets";
    import Button from "$lib/atoms/button.svelte";
    import { onMount } from "svelte";
    import { TOAST } from "$lib/stores/toast";
    import { navigationAllowed } from "../../lib/organisms/navbar.svelte";
    import { networking } from "$lib/stores/networking";
    import Icon from "$lib/atoms/icon.svelte";
    import type { Abstract } from "blockly/core/events/events_abstract";
    import type { Snippet } from "$lib/types/bindings/Snippet";
    import SnippetCard from "./partials/SnippetCard.svelte";
    import { dashboard } from "$lib/stores/dashboard";
    import LabeledNumberinput from "$lib/molecules/labeled_numberinput.svelte";
    import type { InstructionsWithValue } from "$lib/types/bindings/InstructionsWithValue";
    import { get } from "svelte/store";
    import Select from "$lib/atoms/select.svelte";

    let needsSaving = $state(false);
    let savingInProcess = $state(false);

    onMount(() => {
        if (needsSaving && selectedSnippet) {
            window.addEventListener("beforeunload", (event) => {
                event.preventDefault();
                event.returnValue = "";
            });
        }
        return () => navigationAllowed.set(true);
    });

    let isBuildable = $state(false);

    let blocklyBackpack: backpackPkg.Backpack | undefined = $state();

    function handleBlocklyChange(e?: Abstract) {
        setTimeout(() => {
            try {
                JSON.parse(generateCode());
                isBuildable = true;
            } catch {
                isBuildable = false;
            }
            const allBlocks = Blockly.getMainWorkspace().getAllBlocks();
            allBlocks.forEach((block) => {
                if (block.previousConnection?.targetConnection === null) {
                    block.setWarningText("Not connected");
                } else {
                    block.setWarningText(null);
                }
            });

            const allBlocksWithEmptyValueInputs = allBlocks.filter((block) =>
                block.inputList.find(
                    (input) =>
                        input.type === 1 && !input.connection?.targetConnection,
                ),
            );

            allBlocksWithEmptyValueInputs.forEach((block) => {
                block.setWarningText("No value connected");
            });

            allBlocks.forEach((block) => {
                // @ts-ignore -> Blockly Blocks do not have a `dropdownsFilled` but we append that to some blocks. Here we check for its existens, so this will not result in an error
                if (block.dropdownsFilled && !block.dropdownsFilled()) {
                    block.setWarningText('Dropdown should not be "-"');
                    // @ts-ignore -> Blockly Blocks do not have a `dropdownsFilled` but we append that to some blocks. Here we check for its existens, so this will not result in an error
                } else if (block.dropdownsFilled && block.dropdownsFilled()) {
                    block.setWarningText(null);
                }
            });

            needsSaving =
                (e &&
                    (e.type === "move" ||
                        e.type === "change" ||
                        e.type === "create" ||
                        e.type === "delete")) ||
                needsSaving;
        });
    }

    let selectedSnippet: Snippet | undefined = $state();

    // @ts-ignore -> This would just be much of repetitive code, so we omit that here
    let blockInstanciators: BlockInstanciator = $state({});

    $effect(() => navigationAllowed.set(!needsSaving));

    $effect(() => {
        if (
            selectedSnippet &&
            selectedSnippet.category &&
            $fixtures &&
            !savingInProcess
        ) {
            {
                Blockly.getMainWorkspace().removeChangeListener(
                    handleBlocklyChange,
                );
                const workspace = Blockly.getMainWorkspace();
                if (workspace) {
                    if (
                        selectedSnippet.do_not_use_instructions &&
                        `snippet-${selectedSnippet.id}` in localStorage
                    ) {
                        const state =
                            localStorage.getItem(
                                `snippet-${selectedSnippet.id}`,
                            ) ?? "";
                        const parsed_state = JSON.parse(state);
                        Blockly.serialization.workspaces.load(
                            parsed_state,
                            workspace,
                        );
                        if (blocklyBackpack) {
                            restoreBackpack(blocklyBackpack);
                        }
                    } else {
                        workspace.clear();
                        if (selectedSnippet.category === "Startup") {
                            blockInstanciators["Startup"](
                                workspace,
                                selectedSnippet.instructions,
                            );
                        } else if (selectedSnippet.category === "Watcher") {
                            blockInstanciators["Watcher"](
                                workspace,
                                selectedSnippet.instructions,
                            );
                        } else if (selectedSnippet.category === "Callable") {
                            blockInstanciators["Callable"](
                                workspace,
                                selectedSnippet.instructions,
                            );
                        } else {
                            blockInstanciators["InputChanged"](
                                workspace,
                                selectedSnippet.instructions,
                            );
                        }

                        if (blocklyBackpack) {
                            restoreBackpack(blocklyBackpack);
                        }
                        // @ts-ignore -> I have no idea why ts complains here. We need this
                        workspace.render();
                    }
                }
                setTimeout(() => handleBlocklyChange(), 0);
                setTimeout(
                    () =>
                        Blockly.getMainWorkspace().addChangeListener(
                            handleBlocklyChange,
                        ),
                    1000,
                );
            }
        }

        return () => {
            const workspace = Blockly.getMainWorkspace();
            if (workspace) {
                workspace.removeChangeListener(handleBlocklyChange);
            }
        };
    });

    const patchSnippetInstruction = async () => {
        if (savingInProcess) return;
        savingInProcess = true;
        return new Promise(async (resolve) => {
            if (selectedSnippet) {
                if (Blockly.getMainWorkspace()) {
                    try {
                        generateCode();
                        if (isBuildable) {
                            const generatedCode = generateCode();
                            try {
                                await Promise.all([
                                    snippetDir.setSnippetInstructions(
                                        selectedSnippet.id,
                                        JSON.parse(generatedCode),
                                    ),
                                    snippetDir.setSnippetDoNotUseInstructions(
                                        selectedSnippet.id,
                                        false,
                                    ),
                                    snippetDir.setSnippetRequiresUserActionReason(
                                        selectedSnippet.id,
                                        null,
                                    ),
                                ]);
                                localStorage.removeItem(
                                    `snippet-${selectedSnippet.id}`,
                                );
                                const foundDucktypedSnippet = allSnippetsOf(
                                    $snippetDir,
                                ).find(
                                    (snippetFromAllSnippets) =>
                                        selectedSnippet &&
                                        snippetFromAllSnippets.name ===
                                            selectedSnippet.name &&
                                        snippetFromAllSnippets.category ===
                                            selectedSnippet?.category &&
                                        snippetFromAllSnippets.serial_module_key ===
                                            selectedSnippet.serial_module_key,
                                );
                                TOAST.success(
                                    "The snippet has been saved successfully!",
                                );
                                if (foundDucktypedSnippet) {
                                    TOAST.info(
                                        "Please note that the displayed instructions may not match the snippet. If something seems off, try clicking another snippet in the directory and then return to this one. This can happen, because we skip some assertions to let the matching as fast as possible",
                                    );
                                    selectedSnippet.instructions =
                                        foundDucktypedSnippet.instructions;
                                }
                            } catch (e) {
                                TOAST.warning(
                                    "Please make sure to interconnect all displayed blocks (directly or indirectly)." +
                                        " Also make sure to get rid of all displayed warnings. (The little blue triangle on the blocks)",
                                );
                                return;
                            }
                        } else {
                            const snapshot = generateWorkspaceSnapshot();
                            if (snapshot.backpack) {
                                delete snapshot.backpack;
                            }
                            localStorage.setItem(
                                `snippet-${selectedSnippet.id}`,
                                JSON.stringify(snapshot),
                            );
                            await snippetDir.setSnippetRequiresUserActionReason(
                                selectedSnippet.id,
                                "This is not buildable right now",
                            );
                            await snippetDir.setSnippetDoNotUseInstructions(
                                selectedSnippet.id,
                                true,
                            );
                        }
                    } catch (e) {
                        console.error("hitting");
                        TOAST.error(String(e));
                        return;
                    }
                }
                needsSaving = false;
                savingInProcess = false;
                resolve(null);
            }
        });
    };

    async function setModuleKeyToRecentlyActivated() {
        if (selectedSnippet) {
            fetch(`http://${get(networking)}:${networking.port}/last_keyevent`)
                .then(async (data) => {
                    if (selectedSnippet) {
                        let newKey: number | undefined = undefined;
                        if (data.status === 200) {
                            newKey = await data.json();
                        }
                        const promptResult = prompt(
                            `Set the new key of snippet ${selectedSnippet.name}.\n(Prefilled with most recent key pressed, if there was any)`,
                            newKey?.toString() ?? "",
                        );
                        if (promptResult !== null) {
                            if (
                                typeof promptResult === "string" &&
                                promptResult.length > 0
                            ) {
                                let promptResultAsNumber =
                                    parseInt(promptResult);
                                if (promptResultAsNumber > -1) {
                                    if (
                                        allSnippetsOf($snippetDir).find(
                                            (snippet) =>
                                                selectedSnippet &&
                                                snippet.id !==
                                                    selectedSnippet.id &&
                                                promptResultAsNumber ===
                                                    snippet.serial_module_key,
                                        )
                                    ) {
                                        TOAST.warning("Key is already in use!");
                                    } else {
                                        snippetDir.setSnippetSerialModuleKey(
                                            selectedSnippet.id,
                                            promptResultAsNumber,
                                        );
                                        selectedSnippet.serial_module_key =
                                            promptResultAsNumber;
                                    }
                                } else {
                                    TOAST.warning(
                                        "Please enter a valid number",
                                    );
                                }
                            } else if (typeof promptResult === "string") {
                                snippetDir.setSnippetSerialModuleKey(
                                    selectedSnippet.id,
                                    null,
                                );
                                selectedSnippet.serial_module_key = null;
                            } else {
                                TOAST.warning("Please enter a valid number");
                            }
                        }
                    }
                })
                .catch(() =>
                    TOAST.error("Could not find a recently pressed key"),
                );
        }
    }

    let bindableBpm = $derived.by(() => {
        if ($dashboard) {
            return $dashboard.bpm;
        } else {
            return 0;
        }
    });

    async function changeBpm(bpm: number) {
        let instructions_with_value: InstructionsWithValue = {
            instructions: [
                {
                    instruction: "BpmTo",
                    instructionMod: "Value",
                },
            ],
            value: bpm,
        };
        fetch(
            `http://${get(networking)}:${networking.port}/one_time_instructions`,
            {
                method: "PUT",
                body: JSON.stringify(instructions_with_value),
                headers: new Headers({
                    "content-type": "application/json",
                }),
            },
        );
    }
</script>

<div class="flex">
    <div class="w-fit rounded-lg bg-object p-6 drop-shadow">
        <div class="flex flex-col space-y-2 rounded-lg bg-primary p-2">
            <!-- TODO: this is only for dev reasons here and should realy have its own spot or be removed entirely -->
            <div class="flex justify-center rounded-lg bg-object p-4">
                <LabeledNumberinput
                    label="Bpm"
                    value={bindableBpm}
                    onchange={(e) => changeBpm(e)}
                />
            </div>
            <div class="rounded-lg bg-object p-4">
                <SnippetCard
                    bind:selectedSnippet
                    {needsSaving}
                    {isBuildable}
                    {savingInProcess}
                    updateKeyToRecentlyPressed={() =>
                        setModuleKeyToRecentlyActivated()}
                    updateInstructions={async () => {
                        needsSaving = true;
                        await patchSnippetInstruction();
                    }}
                ></SnippetCard>
            </div>
        </div>
    </div>

    {#if selectedSnippet && $snippetDir && allSnippetsOf($snippetDir)}
        {#key selectedSnippet.category}
            <div id="app">
                <BlocklyComponent
                    bind:blockinstanciators={blockInstanciators}
                    bind:isBuildable
                    bind:blocklyBackpack
                    {selectedSnippet}
                >
                    <category
                        name="Control Flow"
                        colour={CATEGORY_COLOR.CONTROLFLOW}
                    >
                        <block type="valueconst"></block>
                        <block type="valueinput"></block>
                        <block type="pressedconst"></block>
                        <block type="releasedconst"></block>
                        <block type="defaultconst"></block>
                        <block type="fixtureloop"></block>
                        <block type="ifstatement"></block>
                        {#if selectedSnippet.category !== "Callable"}
                            <block type="loopstatement"></block>
                            <block type="ifqueueallowscontinue"></block>
                        {/if}
                    </category>
                    <category name="Special" colour={CATEGORY_COLOR.TRIGGER}>
                        <block type="executecallablesnippet"></block>
                        <block type="togglequeuemode"></block>
                        <block type="startrecording"></block>
                        <block type="stoprecording"></block>
                        <block type="clearrecording"></block>
                        <block type="print"></block>
                    </category>
                    <category name="Fixtures" colour={CATEGORY_COLOR.FIXTURES}>
                        <block type="dimmerto"></block>
                        <block type="activateactionfixtureid"></block>
                        <block type="activateactionfixturegroup"></block>
                        <block type="activateactionallfixtures"></block>
                        <block type="keypoint"></block>
                        <block type="addtogroup"></block>
                        <block type="removefromgroup"></block>
                        <block type="unimplementedchannelto"></block>
                        <block type="getfixturefunction"></block>
                        <block type="positionto"></block>
                        <block type="panto"></block>
                        <block type="tiltto"></block>
                        <block type="colorto"></block>
                        <block type="colortorandom"></block>
                        <block type="blueprintto"></block>
                        <block type="setspeedofblueprints"></block>
                        <block type="getblueprintspeed"></block>
                        <block type="setblueprintintensity"></block>
                        <block type="getblueprintintensity"></block>
                        <block type="setblueprintpositionindexoffsetmode"
                        ></block>
                        <block type="clearblueprint"></block>
                    </category>
                    <category name="Math" colour={CATEGORY_COLOR.VALUES}>
                        <block type="random"></block>
                        <block type="mathoperator"></block>
                    </category>
                    <category
                        name="Variables"
                        colour={CATEGORY_COLOR.VARIABLES}
                    >
                        <block type="createvariable"></block>
                        <block type="setvariable"></block>
                        <block type="getvariable"></block>
                    </category>
                    <category name="Timing" colour={CATEGORY_COLOR.TIME}>
                        <block type="programstarttimestamp"></block>
                        <block type="bpmto"></block>
                        <block type="bpm"></block>
                        <block type="bpmmodifierto"></block>
                        <block type="bpmmodifier"></block>
                        {#if selectedSnippet.category !== "Callable"}
                            <block type="delaybynotes"></block>
                        {/if}
                    </category>
                </BlocklyComponent>
            </div>
        {/key}
    {/if}
</div>

<style>
    #app {
        font-family: "Avenir", Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-align: center;
        color: #2c3e50;
    }
</style>
