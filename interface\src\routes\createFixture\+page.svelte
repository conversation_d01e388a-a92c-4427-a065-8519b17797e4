<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import type { DmxFixtureFileDescriptor } from "$lib/types/bindings/DmxFixtureFileDescriptor";
    import type { MovementChannels } from "$lib/types/bindings/MovementChannels";
    import { fixtures } from "$lib/stores/fixtures";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import LabeledNumberinput from "$lib/molecules/labeled_numberinput.svelte";
    import LabeledDropdown from "$lib/molecules/labeled_dropdown.svelte";
    import ExistingFixtures from "./partials/existingFixtures.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import clone from "just-clone";
    import {
        appendSnippetEditingReason,
        allLeafesOf,
    } from "$lib/stores/snippets";
    import type { UnimplementedChannel } from "$lib/types/bindings/UnimplementedChannel";
    import { TOAST } from "$lib/stores/toast";
    import GdtfShareIntegration from "$lib/organisms/GdtfShareIntegration.svelte";
    import { type GdtfFixtureFileDescriptor } from "$lib/types/bindings/GdtfFixtureFileDescriptor";

    let colorChannels:
        | ""
        | "RgbChannels"
        | "ColorWheel"
        | "CmyChannels"
        | "HsvChannels"
        | "XyzChannels" = $state("");

    let amount_to_create: number = $state(1);

    let unimplemented_channels: UnimplementedChannel[] = $state([]);
    function add_unimplemented_channel() {
        unimplemented_channels = [
            ...unimplemented_channels,
            {
                id: null,
                name: "",
                channel: 0,
                default: 0,
                dimmable: false,
                inverted: false,
                keypoints: [],
            },
        ];
    }

    let hasMovementChannels = $state(false);
    let hasColorChannel = $derived(!!colorChannels);
    function emptyMovementChannels(): MovementChannels {
        return { pan: { channel: -1 }, tilt: { channel: -1 } };
    }
    let newMovementChannels = $state(emptyMovementChannels());

    function emptyFixture(): DmxFixtureFileDescriptor {
        return {
            id: null,
            name: "",
            fixturetype: "",
            movement_channels: null,
            color: null,
            footprint_size: 0,
            dmx_address: null,
            dmx_universe: 1,
            stage_coordinates: [0, 0],
            unimplemented_channels: [],
        };
    }

    let newFixture = $state(emptyFixture());

    function clearForm() {
        newFixture = emptyFixture();

        hasMovementChannels = false;
        newMovementChannels = emptyMovementChannels();

        colorChannels = "";
        r = null;
        g = null;
        b = null;
        c = null;
        m = null;
        y = null;
        h = null;
        s = null;
        v = null;
        x = null;
        yy = null;
        z = null;

        channel = -1;
        red = -1;
        green = -1;
        blue = -1;
        light_blue = -1;
        purple = -1;
        yellow = -1;
        pink = -1;
        orange = -1;
        white = -1;

        unimplemented_channels = [];

        amount_to_create = 1;

        editingFixtureId = null;
    }
    function assertNewFixture(): boolean {
        if (
            newFixture.name === "" &&
            newFixture.fixturetype === "" &&
            newFixture.footprint_size === 0 &&
            newFixture.stage_coordinates
        ) {
            TOAST.error("newFixture general assertion not successful");
            return false;
        }

        if (
            $fixtures.find(
                (fixture) => fixture.fixturetype === newFixture.fixturetype,
            ) &&
            !editingFixtureId
        ) {
            TOAST.error(`fixturetype ${newFixture.fixturetype} already exists`);
            return false;
        }

        return true;
    }

    async function addFixture() {
        if (assertNewFixture()) {
            if (hasMovementChannels) {
                newFixture.movement_channels = newMovementChannels;
            } else {
                newFixture.movement_channels = null;
            }
            applyColorChannelsToFixture(newFixture);
            if (unimplemented_channels) {
                newFixture.unimplemented_channels = unimplemented_channels;
            }
            newFixture.dmx_address = null;
            let toAddFixtures = [];
            for (let i = 0; i < amount_to_create; i++) {
                newFixture.stage_coordinates = [
                    Math.floor(Math.random() * 1024),
                    Math.floor(Math.random() * 1024),
                ];
                if (amount_to_create > 1) {
                    newFixture.name += "_" + (i + 1);
                }
                toAddFixtures.push(clone(newFixture));
                newFixture.name = newFixture.name.slice(
                    0,
                    newFixture.name.lastIndexOf("_"),
                );
            }
            await fixtures.create(toAddFixtures);
            clearForm();
        }
    }
    function applyColorChannelsToFixture(fixture: DmxFixtureFileDescriptor) {
        if (colorChannels === "RgbChannels") {
            fixture.color = {
                color: { red: 0, green: 0, blue: 0 },
                channels: {
                    RgbChannels: {
                        r,
                        g,
                        b,
                    },
                },
            };
        }
        if (colorChannels === "CmyChannels") {
            fixture.color = {
                color: { red: 0, green: 0, blue: 0 },
                channels: {
                    CmyChannels: {
                        c,
                        m,
                        y,
                    },
                },
            };
        }
        if (colorChannels === "HsvChannels") {
            fixture.color = {
                color: { red: 0, green: 0, blue: 0 },
                channels: {
                    HsvChannels: {
                        h,
                        s,
                        v,
                    },
                },
            };
        }
        if (colorChannels === "XyzChannels") {
            fixture.color = {
                color: { red: 0, green: 0, blue: 0 },
                channels: {
                    XyzChannels: {
                        x,
                        y,
                        yy,
                    },
                },
            };
        }
        if (colorChannels === "ColorWheel") {
            fixture.color = {
                color: { red: 0, green: 0, blue: 0 },
                channels: {
                    ColorWheel: {
                        channel,
                        red,
                        green,
                        blue,
                        light_blue,
                        purple,
                        yellow,
                        pink,
                        orange,
                        white,
                    },
                },
            };
        }
    }

    function fillFormFromFixture(
        fixture: DmxFixtureFileDescriptor | GdtfFixtureFileDescriptor,
    ) {
        clearForm();
        if ("name" in fixture) {
            newFixture.name = fixture.name;
        }
        newFixture.fixturetype = fixture.fixturetype;
        newFixture.footprint_size = fixture.footprint_size;
        if (fixture.movement_channels) {
            hasMovementChannels = true;
        }
        newMovementChannels = fixture.movement_channels ?? newMovementChannels;
        unimplemented_channels = fixture.unimplemented_channels;

        if (fixture.color) {
            if ("RgbChannels" in fixture.color.channels) {
                colorChannels = "RgbChannels";
                r = fixture.color.channels.RgbChannels.r;
                g = fixture.color.channels.RgbChannels.g;
                b = fixture.color.channels.RgbChannels.b;
            }
            if ("HsvChannels" in fixture.color.channels) {
                colorChannels = "HsvChannels";
                h = fixture.color.channels.HsvChannels.h;
                s = fixture.color.channels.HsvChannels.s;
                v = fixture.color.channels.HsvChannels.v;
            }
            if ("CmyChannels" in fixture.color.channels) {
                colorChannels = "CmyChannels";
                c = fixture.color.channels.CmyChannels.c;
                m = fixture.color.channels.CmyChannels.m;
                y = fixture.color.channels.CmyChannels.y;
            }
            if ("XyzChannels" in fixture.color.channels) {
                colorChannels = "XyzChannels";
                x = fixture.color.channels.XyzChannels.x;
                yy = fixture.color.channels.XyzChannels.y;
                z = fixture.color.channels.XyzChannels.yy;
            }
            if ("ColorWheel" in fixture.color.channels) {
                colorChannels = "ColorWheel";
                channel = fixture.color.channels.ColorWheel.channel;
                red = fixture.color.channels.ColorWheel.red;
                green = fixture.color.channels.ColorWheel.green;
                blue = fixture.color.channels.ColorWheel.blue;
                light_blue = fixture.color.channels.ColorWheel.light_blue;
                purple = fixture.color.channels.ColorWheel.purple;
                yellow = fixture.color.channels.ColorWheel.yellow;
                pink = fixture.color.channels.ColorWheel.pink;
                orange = fixture.color.channels.ColorWheel.orange;
                white = fixture.color.channels.ColorWheel.white;
            }
        }
    }

    let editingFixtureId: number | null = $state(null);
    function editFixture(fixture: DmxFixtureFileDescriptor) {
        fillFormFromFixture(fixture);
        if (typeof fixture.id === "number") {
            editingFixtureId = fixture.id;
        }
        amount_to_create = 1;
    }

    async function cloneFixture(fixture: DmxFixtureFileDescriptor) {
        fixture.name = fixture.name + "_clone";
        await fixtures.create([fixture]);
        TOAST.success(
            `Created new fixture: ${fixture.name} from fixturetype ${fixture.fixturetype}`,
        );
    }

    async function applyEdit() {
        $fixtures.forEach((fixture) => {
            if (
                fixture.fixturetype ===
                $fixtures.find((fixture) => fixture.id === editingFixtureId)
                    ?.fixturetype
            ) {
                if (assertNewFixture()) {
                    fixture.name = newFixture.name;
                    fixture.fixturetype = newFixture.fixturetype;
                    fixture.footprint_size = newFixture.footprint_size;
                    if (hasColorChannel) {
                        applyColorChannelsToFixture(fixture);
                    } else {
                        fixture.color = null;
                    }
                    if (hasMovementChannels) {
                        fixture.movement_channels = newMovementChannels;
                    } else {
                        fixture.movement_channels = null;
                    }
                    if (unimplemented_channels) {
                        fixture.unimplemented_channels = unimplemented_channels;
                    }
                    appendSnippetEditingReason(
                        (snippet) =>
                            snippet.instructions
                                .flatMap((instruction) =>
                                    allLeafesOf(instruction),
                                )
                                .some((instruction) => {
                                    if ("instruction" in instruction) {
                                        let result = false;
                                        if (
                                            "ActivateFixtureById" ===
                                            instruction.instruction
                                        ) {
                                            result =
                                                instruction.instructionMod ===
                                                newFixture.id;
                                        } else if (
                                            "UnimplementedChannelTo" ===
                                            instruction.instruction
                                        ) {
                                            result =
                                                newFixture.unimplemented_channels.some(
                                                    (newChannel) =>
                                                        instruction
                                                            .instructionMod
                                                            .name ===
                                                        newChannel.name,
                                                );
                                        }
                                        return result;
                                    }
                                    return false;
                                }),
                        `edited fixturetype ${
                            $fixtures.find(
                                (fixture) => fixture.id === editingFixtureId,
                            )?.fixturetype
                        }`,
                    );
                }
            }
        });
        await fixtures.updateRemote();
        TOAST.success(
            `Fixturetype edited: ${
                $fixtures.find((fixture) => fixture.id === editingFixtureId)
                    ?.fixturetype
            }`,
        );
        clearForm();
    }
    // color channel bindings
    let r: number | null = $state(null);
    let g: number | null = $state(null);
    let b: number | null = $state(null);
    let c: number | null = $state(null);
    let m: number | null = $state(null);
    let y: number | null = $state(null);
    let h: number | null = $state(null);
    let s: number | null = $state(null);
    let v: number | null = $state(null);
    let x: number | null = $state(null);
    let yy: number | null = $state(null);
    let z: number | null = $state(null);

    // color wheel bindings
    let channel = $state(-1);
    let red = $state(-1);
    let green = $state(-1);
    let blue = $state(-1);
    let light_blue = $state(-1);
    let purple = $state(-1);
    let yellow = $state(-1);
    let pink = $state(-1);
    let orange = $state(-1);
    let white = $state(-1);

    function handleGdtfFixtureSelected(fixture: GdtfFixtureFileDescriptor) {
        fillFormFromFixture(fixture);

        TOAST.success(`Fixture loaded from GDTF-Share: ${fixture.fixturetype}`);
    }
</script>

<div class="flex flex-col">
    <div class="rounded-lg bg-object">
        <div class="flex space-x-4 p-8">
            <div class="flex flex-col space-y-4">
                <LabeledTextinput
                    label="Fixturetype"
                    bind:value={newFixture.fixturetype}
                    disabled={editingFixtureId !== null}
                />
                {#if editingFixtureId === null}
                    <LabeledTextinput
                        label="name"
                        bind:value={newFixture.name}
                    />
                {/if}
                <LabeledNumberinput
                    label="Footprintsize"
                    bind:value={newFixture.footprint_size}
                ></LabeledNumberinput>
                {#if editingFixtureId === null}
                    <LabeledNumberinput
                        label="Amount to create"
                        bind:value={amount_to_create}
                    ></LabeledNumberinput>
                {/if}
            </div>
            <div class="flex">
                <div>
                    <div class="mb-2">
                        <LabeledDropdown
                            label="Colorchannel type"
                            bind:value={colorChannels}
                        >
                            <option value="">-</option>
                            <option value="RgbChannels">RGB Channels</option>
                            <option value="ColorWheel">Color Wheel</option>
                            <option value="CmyChannels">CMY Channels</option>
                            <option value="HsvChannels">HSV Channels</option>
                            <option value="XyzChannels"
                                >XYZ (CIE) Channels</option
                            >
                        </LabeledDropdown>
                    </div>
                    {#if colorChannels === "RgbChannels"}
                        <div class="flex flex-col space-y-4">
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="red-channel"
                                    onclick={() => {
                                        r = r === null ? 0 : null;
                                    }}
                                >
                                    <Icon
                                        icon={r !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if r !== null}
                                    <LabeledNumberinput
                                        label="(R)ed channel"
                                        bind:value={r}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (R)ed channel - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="green-channel"
                                    onclick={() => (g = g === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={g !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if g !== null}
                                    <LabeledNumberinput
                                        label="(G)reen channel"
                                        bind:value={g}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (G)reen channel - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="blue-channel"
                                    onclick={() => (b = b === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={b !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if b !== null}
                                    <LabeledNumberinput
                                        label="(B)lue channel"
                                        bind:value={b}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (B)lue channel - disabled
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {:else if colorChannels === "CmyChannels"}
                        <div class="flex flex-col space-y-4">
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="cyan-channel"
                                    onclick={() => (c = c === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={c !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if c !== null}
                                    <LabeledNumberinput
                                        label="(C)yan channel"
                                        bind:value={c}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (C)yan channel - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="magenta-channel"
                                    onclick={() => (m = m === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={m !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if m !== null}
                                    <LabeledNumberinput
                                        label="(M)agenta channel"
                                        bind:value={m}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (M)agenta channel - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="yellow-channel"
                                    onclick={() => (y = y === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={y !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if y !== null}
                                    <LabeledNumberinput
                                        label="(Y)ellow channel"
                                        bind:value={y}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (Y)ellow channel - disabled
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {:else if colorChannels === "HsvChannels"}
                        <div class="flex flex-col space-y-4">
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="hue-channel"
                                    onclick={() => (h = h === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={h !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if h !== null}
                                    <LabeledNumberinput
                                        label="(H)ue channel"
                                        bind:value={h}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (H)ue channel - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="saturation-channel"
                                    onclick={() => (s = s === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={s !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if s !== null}
                                    <LabeledNumberinput
                                        label="(S)aturation channel"
                                        bind:value={s}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (S)aturation channel - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="value-channel"
                                    onclick={() => (v = v === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={v !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if v !== null}
                                    <LabeledNumberinput
                                        label="(V)alue channel"
                                        bind:value={v}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        (V)alue channel - disabled
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {:else if colorChannels === "XyzChannels"}
                        <div class="flex flex-col space-y-4">
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="x-channel"
                                    onclick={() => (x = x === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={x !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if x !== null}
                                    <LabeledNumberinput
                                        label="RGB curves channel (X)"
                                        bind:value={x}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        RGB curves channel (X) - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="y-channel"
                                    onclick={() =>
                                        (yy = yy === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={yy !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if yy !== null}
                                    <LabeledNumberinput
                                        label="Luminance channel (Y)"
                                        bind:value={yy}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        Luminance channel (Y) - disabled
                                    </div>
                                {/if}
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button
                                    id="z-channel"
                                    onclick={() => (z = z === null ? 0 : null)}
                                >
                                    <Icon
                                        icon={z !== null
                                            ? "mdi:check-circle"
                                            : "mdi:close"}
                                    ></Icon>
                                </Button>
                                {#if z !== null}
                                    <LabeledNumberinput
                                        label="~Blue channel (Z)"
                                        bind:value={z}
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                {:else}
                                    <div class="text-gray-400">
                                        ~Blue channel (Z) - disabled
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {:else if colorChannels === "ColorWheel"}
                        <div class="flex flex-col space-y-4">
                            <LabeledNumberinput
                                label="channel"
                                bind:value={channel}
                                min={1}
                                max={512}
                            ></LabeledNumberinput>
                            <LabeledNumberinput label="red" bind:value={red}
                            ></LabeledNumberinput>
                            <LabeledNumberinput label="green" bind:value={green}
                            ></LabeledNumberinput>
                            <LabeledNumberinput label="blue" bind:value={blue}
                            ></LabeledNumberinput>
                            <LabeledNumberinput
                                label="light_blue"
                                bind:value={light_blue}
                            ></LabeledNumberinput>
                            <LabeledNumberinput
                                label="purple"
                                bind:value={purple}
                            ></LabeledNumberinput>
                            <LabeledNumberinput label="pink" bind:value={pink}
                            ></LabeledNumberinput>
                            <LabeledNumberinput
                                label="orange"
                                bind:value={orange}
                            ></LabeledNumberinput>
                            <LabeledNumberinput
                                label="yellow"
                                bind:value={yellow}
                            ></LabeledNumberinput>
                            <LabeledNumberinput label="white" bind:value={white}
                            ></LabeledNumberinput>
                        </div>
                    {/if}
                </div>
            </div>
            <div>
                <div class="mt-2 flex">
                    {#if !hasMovementChannels}
                        <Button
                            id="add-movement-channels"
                            onclick={() => (hasMovementChannels = true)}
                        >
                            <div class="flex">
                                <div class="mt-1">
                                    <Icon icon="mdi:add"></Icon>
                                </div>
                                <p class="ml-1">Pan/Tilt channels</p>
                            </div>
                        </Button>
                    {/if}
                    {#if hasMovementChannels}
                        <div class="rounded-lg bg-primary p-2">
                            <div class="rouned-lg flex space-x-4 bg-object p-2">
                                {#if newMovementChannels.pan && newMovementChannels.tilt}
                                    <LabeledNumberinput
                                        bind:value={
                                            newMovementChannels.pan.channel
                                        }
                                        label="Pan"
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                    <LabeledNumberinput
                                        bind:value={
                                            newMovementChannels.tilt.channel
                                        }
                                        label="Tilt"
                                        min={1}
                                        max={512}
                                    ></LabeledNumberinput>
                                    <Button
                                        id="removeMovementChannels"
                                        onclick={() => {
                                            hasMovementChannels = false;
                                        }}
                                    >
                                        <Icon icon="mdi:garbage-can-empty"
                                        ></Icon>
                                    </Button>
                                {/if}
                            </div>
                        </div>
                    {/if}
                </div>
                <div
                    class="mt-2.5 {unimplemented_channels.length > 0
                        ? 'rounded-lg border border-input bg-primary p-2'
                        : ''}"
                >
                    <div>
                        <Button
                            id="addUnimplementedChannel"
                            onclick={add_unimplemented_channel}
                        >
                            <div class="flex">
                                <div class="mt-1">
                                    <Icon icon="mdi:add"></Icon>
                                </div>
                                <p class="ml-1">other channel</p>
                            </div>
                        </Button>
                    </div>
                    {#each unimplemented_channels as unimplemented_channel, index (index)}
                        <div
                            class="mt-4 flex flex-col w-fit space-x-4 rounded-lg border border-input bg-object p-2"
                            id={`unimplemented-channel-${index}`}
                        >
                            <div class="flex">
                                <LabeledTextinput
                                    label={"Name"}
                                    bind:value={unimplemented_channel.name}
                                />
                                <LabeledNumberinput
                                    label={"Channel"}
                                    bind:value={unimplemented_channel.channel}
                                    min={1}
                                    max={512}
                                ></LabeledNumberinput>
                                <LabeledNumberinput
                                    label={"Default"}
                                    bind:value={unimplemented_channel.default}
                                ></LabeledNumberinput>
                                <div class="flex flex-col">
                                    <p>Effected by "master dimmer"</p>
                                    <input
                                        type="checkbox"
                                        bind:checked={
                                            unimplemented_channel.dimmable
                                        }
                                    />
                                </div>
                                <div class="flex flex-col">
                                    <p>Inverted</p>
                                    <input
                                        type="checkbox"
                                        bind:checked={
                                            unimplemented_channel.inverted
                                        }
                                    />
                                </div>
                                <Button
                                    id="remove-unimplemented-channel"
                                    onclick={() => {
                                        unimplemented_channels.splice(index, 1);
                                        unimplemented_channels =
                                            unimplemented_channels;
                                    }}
                                >
                                    <Icon icon="mdi:garbage-can-empty"></Icon>
                                </Button>
                            </div>

                            <div class="my-2">
                                {#each unimplemented_channel.keypoints as keypoint, index (index)}
                                    <div
                                        id={`keypoint-${index}`}
                                        class="flex flex-row"
                                    >
                                        <LabeledTextinput
                                            label={"Name"}
                                            bind:value={keypoint.name}
                                        />
                                        <LabeledNumberinput
                                            label={"Value"}
                                            bind:value={keypoint.value}
                                            max={255}
                                            min={0}
                                        ></LabeledNumberinput>
                                        <Button
                                            id="remove-keypoint"
                                            onclick={() => {
                                                unimplemented_channel.keypoints.splice(
                                                    index,
                                                    1,
                                                );
                                            }}
                                        >
                                            <Icon icon="mdi:garbage-can-empty"
                                            ></Icon>
                                        </Button>
                                    </div>
                                {/each}
                            </div>
                            <div class="flex">
                                <Button
                                    id="add-keypoint"
                                    onclick={() =>
                                        unimplemented_channel.keypoints.push({
                                            id: null,
                                            name: "",
                                            value: 0,
                                        })}
                                >
                                    <div class="flex">
                                        <div class="mt-1">
                                            <Icon icon="mdi:add"></Icon>
                                        </div>
                                        <p class="ml-1">Keypoint</p>
                                    </div>
                                </Button>
                            </div>
                        </div>
                    {/each}
                </div>
            </div>
        </div>

        <div class="m-4 flex space-x-6">
            <Button id="clear-form" onclick={() => clearForm()}>
                <div class="flex">
                    <div class="mt-1">
                        <Icon icon="mdi:broom"></Icon>
                    </div>
                    <div class="ml-1">clear Form</div>
                </div>
            </Button>
            <Button
                id="edit-fixture"
                onclick={() =>
                    editingFixtureId === null ? addFixture() : applyEdit()}
            >
                <div class="flex">
                    {#if editingFixtureId !== null}
                        <div class="mt-1 pr-1">
                            <Icon icon="solar:pen-bold"></Icon>
                        </div>
                        Edit fixture
                    {:else if amount_to_create > 1}
                        <div class="mt-1">
                            <Icon icon="mdi:add"></Icon>
                        </div>
                        Add Fixtures
                    {:else}
                        <div class="mt-1">
                            <Icon icon="mdi:add"></Icon>
                        </div>
                        fixture
                    {/if}
                </div></Button
            >
        </div>
    </div>

    <div class="flex space-x-2 w-full">
        <div class="mt-10 rounded-lg bg-object p-4">
            <GdtfShareIntegration
                onFixtureSelected={handleGdtfFixtureSelected}
            />
        </div>

        <div class="mt-10 flex justify-between w-full">
            <div class="grow rounded-lg bg-object p-4">
                <ExistingFixtures {editFixture} {cloneFixture}
                ></ExistingFixtures>
            </div>
        </div>
    </div>
</div>
