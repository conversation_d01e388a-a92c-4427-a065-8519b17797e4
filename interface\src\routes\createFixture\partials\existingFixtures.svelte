<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import Numberinput from "$lib/atoms/numberinput.svelte";
    import { fixtures } from "$lib/stores/fixtures";
    import {
        allLeafesOf,
        appendSnippetEditingReason,
    } from "$lib/stores/snippets";
    import type { DmxFixtureFileDescriptor } from "$lib/types/bindings/DmxFixtureFileDescriptor";
    import Icon from "$lib/atoms/icon.svelte";
    import { stringToColor } from "$lib/utils";
    import { TOAST } from "$lib/stores/toast";

    let {
        editFixture,
        cloneFixture,
    }: {
        editFixture: (fixture: DmxFixtureFileDescriptor) => any;
        cloneFixture: (fixture: DmxFixtureFileDescriptor) => any;
    } = $props();

    let needsSaving = $state(false);

    function deleteFixture(fixture: DmxFixtureFileDescriptor) {
        fixtures.delete(fixture);
    }
</script>

<h2 class="flex justify-center">existing fixtures:</h2>
<div class="w-fit">
    {#if needsSaving}
        <button
            class="w-full rounded-lg bg-accent/20 p-4 text-3xl text-blue-500"
            onclick={async () => {
                await fixtures.updateRemote();
                needsSaving = false;
            }}
        >
            <div class="flex justify-center">
                <div class="animate-shake-once">
                    <Icon icon="material-symbols:save"></Icon>
                </div>
            </div>
        </button>
    {:else}
        <div
            class="flex justify-center rounded-lg bg-object p-4 text-3xl text-green-500"
        >
            <Icon icon="material-symbols:save"></Icon>
            <div class="absolute animate-ping-once">
                <Icon icon="material-symbols:save"></Icon>
            </div>
        </div>
    {/if}
</div>
<div class="flex justify-center">
    <div class="flex flex-col">
        {#if $fixtures && $fixtures.length}
            <table class="w-3/4">
                <thead class="border-b">
                    <tr>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            ID
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Type
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Name
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Universe
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Address
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {#each $fixtures as fixture, index}
                        <tr
                            class="cursor-pointer border-b transition"
                            style={`background-color: ${stringToColor(fixture.fixturetype)}80`}
                        >
                            <td class="whitespace-nowrap px-6 py-4 text-sm"
                                >{fixture.id}</td
                            >
                            <td class="whitespace-nowrap px-6 py-4 text-sm"
                                >{fixture.fixturetype}</td
                            >
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.name}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Numberinput
                                    bind:value={fixture.dmx_universe}
                                    min={1}
                                    onchange={() => (needsSaving = true)}
                                    max={65535}
                                ></Numberinput>
                            </td>
                            <td class="flex px-6 py-4 text-sm">
                                <div class="grow">
                                    <Numberinput
                                        bind:value={fixture.dmx_address}
                                        min={1}
                                        onchange={() => (needsSaving = true)}
                                        max={512}
                                    ></Numberinput>
                                </div>

                                <Button
                                    id="fill-dmx-address"
                                    onclick={() => {
                                        if (index > 0) {
                                            fixture.dmx_address =
                                                ($fixtures[index - 1]
                                                    ?.dmx_address ?? 0) +
                                                $fixtures[index - 1]
                                                    ?.footprint_size;
                                        } else {
                                            fixture.dmx_address = 1;
                                        }
                                        needsSaving = true;
                                    }}
                                >
                                    <Icon icon="mdi:wand"></Icon>
                                </Button>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Button
                                    id="copy fixture"
                                    onclick={() => {
                                        cloneFixture(fixture);
                                    }}
                                >
                                    <div
                                        class="h-4 flex flex-col justify-center"
                                    >
                                        <Icon icon="mdi:content-copy"></Icon>
                                    </div>
                                </Button>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Button
                                    id="load-fixture-into-form"
                                    onclick={() => {
                                        editFixture(fixture);
                                    }}
                                >
                                    <Icon icon="solar:pen-bold"></Icon>
                                </Button>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Button
                                    id="delete-fixture"
                                    onclick={() => {
                                        appendSnippetEditingReason(
                                            (snippet) =>
                                                snippet.instructions
                                                    .flatMap((instruction) =>
                                                        allLeafesOf(
                                                            instruction,
                                                        ),
                                                    )
                                                    .some((instruction) => {
                                                        if (
                                                            "instruction" in
                                                            instruction
                                                        ) {
                                                            let result = false;
                                                            if (
                                                                "ActivateFixtureById" ===
                                                                instruction.instruction
                                                            ) {
                                                                result =
                                                                    instruction.instructionMod ===
                                                                    fixture.id;
                                                            } else if (
                                                                "UnimplementedChannelTo" ===
                                                                instruction.instruction
                                                            ) {
                                                                result =
                                                                    fixture.unimplemented_channels.some(
                                                                        (
                                                                            newChannel,
                                                                        ) =>
                                                                            instruction
                                                                                .instructionMod
                                                                                .name ===
                                                                            newChannel.name,
                                                                    );
                                                            }
                                                            return result;
                                                        }
                                                        return false;
                                                    }),
                                            `deleted fixture ${fixture.name}`,
                                        );

                                        deleteFixture(fixture);
                                        TOAST.success(
                                            `Fixture ${fixture.name} deleted`,
                                        );
                                    }}
                                >
                                    <Icon icon="mdi:garbage-can-empty"></Icon>
                                </Button>
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        {/if}
    </div>
</div>
