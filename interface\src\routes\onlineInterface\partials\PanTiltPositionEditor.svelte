<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import { appendBlueprintEditingReason } from "$lib/stores/blueprints";
    import { fixtures } from "$lib/stores/fixtures";
    import { networking } from "$lib/stores/networking";
    import { positions } from "$lib/stores/positions";
    import {
        allLeafesOf,
        appendSnippetEditingReason,
    } from "$lib/stores/snippets";
    import type { InstructionsWithValue } from "$lib/types/bindings/InstructionsWithValue";
    import Icon from "$lib/atoms/icon.svelte";
    import type { FixturePosition } from "$lib/types/bindings/FixturePosition";
    import type { ComposedPanTiltPosition } from "$lib/types/bindings/ComposedPanTiltPosition";
    import { get } from "svelte/store";

    let showTableButtonsOfRow = $state(-1);

    let updateResult = $state(false);

    function triggerFixturePositionCapturing() {
        let new_name = prompt("Positionname");
        if (!new_name || !new_name.length) {
            new_name = "new";
        }
        positions.createNew(new_name);
    }
    async function restorePosition(fixturePositions: ComposedPanTiltPosition) {
        let instructions_with_value: InstructionsWithValue = {
            instructions: [],
            value: 0,
        };
        fixturePositions.fixture_positions.forEach(
            (position: FixturePosition) => {
                let fixture = $fixtures.find(
                    (fixture) => fixture.id === position.fixture_id,
                );
                if (fixture && fixture.id !== null) {
                    instructions_with_value.instructions = [
                        ...instructions_with_value.instructions,
                        {
                            instruction: "FixtureLoop",
                            instructionMod: {
                                fixtures: [
                                    {
                                        instruction: "ActivateFixtureById",
                                        instructionMod: fixture.id,
                                    },
                                ],
                                instructions: [
                                    {
                                        instruction: "PanTo",
                                        instructionMod: {
                                            Number: position.pan,
                                        },
                                    },
                                    {
                                        instruction: "TiltTo",
                                        instructionMod: {
                                            Number: position.tilt,
                                        },
                                    },
                                ],
                            },
                        },
                    ];
                }
            },
        );
        fetch(
            `http://${get(networking)}:${networking.port}/one_time_instructions`,
            {
                method: "PUT",
                body: JSON.stringify(instructions_with_value),
                headers: new Headers({
                    "content-type": "application/json",
                }),
            },
        );
    }
</script>

<div class="w-1/3 rounded-lg bg-object p-4">
    <p class="mb-4 text-center text-xl underline">Pan/Tilt position editor</p>
    <div class="flex justify-end">
        <Button id="create-position" onclick={triggerFixturePositionCapturing}>
            <div class="flex">
                <Icon icon="mdi:add"></Icon>
            </div>
        </Button>
    </div>
    <table class="w-full">
        <thead>
            <tr>
                <td class="text w-full"> Positionname </td>
            </tr>
        </thead>
        <tbody>
            {#each $positions as position, index}
                <tr onmouseenter={() => (showTableButtonsOfRow = index)}>
                    <td
                        class:text-lg={showTableButtonsOfRow === index}
                        class="transition-all"
                        colspan={showTableButtonsOfRow === index ? 1 : 5}
                    >
                        {position.name}
                    </td>
                    {#if showTableButtonsOfRow === index}
                        <td class="w-1/5">
                            <Button
                                id="rename-position"
                                onclick={() => {
                                    let newName = prompt(
                                        "Enter a new name for the position",
                                    );
                                    if (newName) {
                                        positions.rename(newName, position);
                                    }
                                }}
                            >
                                <Icon icon="mdi:rename"></Icon>
                            </Button>
                        </td>
                        <td class="w-1/5">
                            <Button
                                id="restore-position"
                                onclick={() => {
                                    restorePosition(position);
                                }}
                            >
                                <Icon
                                    icon="material-symbols:settings-backup-restore"
                                ></Icon>
                            </Button>
                        </td>
                        <td class="w-1/5">
                            <Button
                                id="update-position"
                                onclick={() => {
                                    positions
                                        .updateFixturePositions(position.id)
                                        .then((result) => {
                                            updateResult = result;
                                            setTimeout(
                                                () => (updateResult = false),
                                                1500,
                                            );
                                        });
                                }}
                            >
                                <div class:text-success={updateResult}>
                                    <Icon icon="material-symbols:save"></Icon>
                                </div>
                            </Button>
                        </td>
                        <td class="w-1/5">
                            <Button
                                id="delete-position"
                                onclick={() => {
                                    if (
                                        confirm(
                                            "Delete position " +
                                                position.name +
                                                "?",
                                        )
                                    ) {
                                        appendSnippetEditingReason(
                                            (snippet) =>
                                                snippet.instructions
                                                    .flatMap((instruction) =>
                                                        allLeafesOf(
                                                            instruction,
                                                        ),
                                                    )
                                                    .some((instruction) => {
                                                        if (
                                                            "instruction" in
                                                            instruction
                                                        ) {
                                                            if (
                                                                "PositionTo" ===
                                                                instruction.instruction
                                                            ) {
                                                                return (
                                                                    instruction.instructionMod ===
                                                                    position.id
                                                                );
                                                            }
                                                        }
                                                        return false;
                                                    }),
                                            `deleted position ${position.name}`,
                                        );
                                        appendBlueprintEditingReason(
                                            (blueprint) =>
                                                blueprint.properties.some(
                                                    (property) => {
                                                        if (
                                                            "PanTiltPositions" in
                                                            property
                                                        ) {
                                                            return property.PanTiltPositions[0].some(
                                                                (
                                                                    panTiltPositionProperty,
                                                                ) =>
                                                                    panTiltPositionProperty.position_name ===
                                                                    position.name,
                                                            );
                                                        }
                                                        return false;
                                                    },
                                                ),
                                            `deleted position ${position.name}`,
                                        );
                                        // appendTimecodeEditingReason(
                                        //     (timecode) =>
                                        //         timecode.timeline.some(
                                        //             (track) =>
                                        //                 track.automations.some(
                                        //                     (property) => {
                                        //                         if (
                                        //                             "PanTiltPositions" in
                                        //                             property.automation
                                        //                         ) {
                                        //                             return property.automation.PanTiltPositions.some(
                                        //                                 (
                                        //                                     panTiltPositionProperty,
                                        //                                 ) =>
                                        //                                     panTiltPositionProperty.name ===
                                        //                                     position.name,
                                        //                             );
                                        //                         }
                                        //                         return false;
                                        //                     },
                                        //                 ),
                                        //         ),
                                        //     `deleted position ${position.name}`,
                                        // );
                                        positions.deletePosition(position);
                                    }
                                }}
                            >
                                <Icon icon="mdi:garbage-can-empty"></Icon>
                            </Button>
                        </td>
                    {/if}
                </tr>
            {/each}
        </tbody>
    </table>
</div>
