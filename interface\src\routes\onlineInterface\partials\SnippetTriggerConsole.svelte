<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import type { Snippet } from "$lib/types/bindings/Snippet";
    import { allSnippetsOf, snippetDir } from "$lib/stores/snippets";
    import { networking } from "$lib/stores/networking";
    import Icon from "$lib/atoms/icon.svelte";
    import Numberinput from "$lib/atoms/numberinput.svelte";
    import { get } from "svelte/store";
    import { keyboardMappings } from "$lib/stores/keyboardMappings";
    import Textinput from "$lib/atoms/textinput.svelte";

    interface Debounced_snippet extends Snippet {
        pressed: boolean;
        value: number;
    }
    let debounced_snippets: Debounced_snippet[] = $state([]);

    snippetDir.subscribe((snippetDir) => {
        let snippets = allSnippetsOf(snippetDir);
        debounced_snippets = snippets
            .filter((snippet) => snippet.category === "Key")
            .filter((snippet) => snippet.serial_module_key !== null)
            .map((snippet) => {
                return {
                    ...snippet,
                    pressed: false,
                    value: 0,
                };
            });
    });

    async function pressKey(key: number | null) {
        if (key !== null) {
            fetch(
                `http://${get(networking)}:${networking.port}/keyevent?${key}=1`,
            );
            let sk = debounced_snippets.find(
                (sk) => sk.serial_module_key === key,
            );
            if (sk) {
                sk.value = 1;
            }
        }
    }
    async function changeKey(key: number | null, value: number) {
        if (key !== null) {
            fetch(
                `http://${get(networking)}:${networking.port}/keyevent?${key}=${value}`,
            );
            let sk = debounced_snippets.find(
                (sk) => sk.serial_module_key === key,
            );
            if (sk) {
                sk.value = value;
            }
        }
    }
    async function releaseKey(key: number | null) {
        if (key !== null) {
            fetch(
                `http://${get(networking)}:${networking.port}/keyevent?${key}=0`,
            );
        }
        let sk = debounced_snippets.find((sk) => sk.serial_module_key === key);
        if (sk) {
            sk.value = 0;
        }
    }
</script>

<svelte:window
    onkeypress={(e) => {
        pressKey(keyboardMappings.getserialModuleKeyFrom(e.key));
    }}
    onkeyup={(e) => {
        releaseKey(keyboardMappings.getserialModuleKeyFrom(e.key));
    }}
/>

<div class="w-1/3 rounded-lg bg-object p-4 text-center">
    {#if debounced_snippets}
        <table class="w-full">
            <thead> </thead>
            <tbody>
                {#each debounced_snippets as snippet}
                    <tr>
                        <td>
                            <div class="mt-2 flex space-x-2">
                                <p class="mt-2">
                                    {snippet.name}
                                </p>
                            </div></td
                        >
                        {#if snippet.serial_module_key !== null}
                            <td>
                                <Button
                                    id="press-key"
                                    onclick={() =>
                                        pressKey(snippet.serial_module_key)}
                                >
                                    <div class="flex">
                                        <div class="mt-1">
                                            <Icon icon="mdi:arrow-down-bold"
                                            ></Icon>
                                        </div>
                                        <div class="ml-1">Press</div>
                                    </div>
                                </Button>
                            </td>
                            <td>
                                <Button
                                    id="release-key"
                                    onclick={() =>
                                        releaseKey(snippet.serial_module_key)}
                                >
                                    <div class="flex">
                                        <div class="mt-1">
                                            <Icon icon="mdi:arrow-up-bold"
                                            ></Icon>
                                        </div>
                                        <div class="ml-1">Release</div>
                                    </div>
                                </Button>
                            </td>
                            <td>
                                <Numberinput
                                    onchange={() =>
                                        changeKey(
                                            snippet.serial_module_key,
                                            snippet.value,
                                        )}
                                    bind:value={snippet.value}
                                    max={255}
                                    min={0}
                                ></Numberinput>
                            </td>
                            <td>
                                <Textinput
                                    onchange={(e) => {
                                        if (
                                            snippet.serial_module_key !== null
                                        ) {
                                            keyboardMappings.setMapping(
                                                snippet.serial_module_key,
                                                e,
                                            );
                                        }
                                    }}
                                    value={keyboardMappings.getKeyboardKeyFrom(
                                        snippet.serial_module_key,
                                    ) ?? undefined}
                                ></Textinput>
                            </td>
                        {:else}
                            <td class="flex justify-center mt-3">
                                <Icon icon="material-symbols:warning"></Icon>
                            </td>
                            <td> NO KEY WAS SET </td>
                        {/if}
                    </tr>
                {/each}
            </tbody>
        </table>
    {/if}
</div>
