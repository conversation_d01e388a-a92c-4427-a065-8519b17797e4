<script lang="ts">
    import { fixtures } from "$lib/stores/fixtures";
    import { networking } from "$lib/stores/networking";
    import type { InstructionsWithValue } from "$lib/types/bindings/InstructionsWithValue";
    import { get } from "svelte/store";

    let hoveringFixtureId: number | null = $state(null);
    let movingFixtureId: number | null = $state(null);

    async function moveFixture(e: MouseEvent) {
        if (e.movementX === 0 && e.movementY === 0) return;

        let fixture = $fixtures.find(
            (fixture) => movingFixtureId === fixture.id,
        );
        if (!fixture) return;
        if (fixture.id === null) return;

        let instructions_with_value: InstructionsWithValue = {
            instructions: [
                {
                    instruction: "FixtureLoop",
                    instructionMod: {
                        fixtures: [
                            {
                                instruction: "ActivateFixtureById",
                                instructionMod: fixture.id,
                            },
                        ],
                        instructions: [
                            {
                                instruction: "AddToPan",
                                instructionMod: {
                                    Number: e.movementX + 128,
                                },
                            },
                            {
                                instruction: "AddToTilt",
                                instructionMod: {
                                    Number: e.movementY + 128,
                                },
                            },
                        ],
                    },
                },
            ],
            value: 0,
        };
        fetch(
            `http://${get(networking)}:${networking.port}/one_time_instructions`,
            {
                method: "PUT",
                body: JSON.stringify(instructions_with_value),
                headers: new Headers({
                    "content-type": "application/json",
                }),
            },
        );
    }
</script>

<svelte:window
    onmousedown={() => (movingFixtureId = hoveringFixtureId)}
    onmouseup={() => (movingFixtureId = null)}
    onmousemove={(e) => (movingFixtureId !== null ? moveFixture(e) : null)}
/>

<div class="w-1/5 rounded-lg bg-object p-4 text-center">
    <p class="mb-4 text-xl underline">Fixture Pan/Tilt</p>
    <p class="text-xs">
        Just click and drag a fixture to move its pan/tilt position in real time
    </p>
    <table class="w-full" onmouseleave={() => (hoveringFixtureId = null)}>
        <thead> </thead>
        <tbody>
            {#each $fixtures as fixture}
                <tr
                    class="flex w-full cursor-move justify-center"
                    onmouseenter={() => (hoveringFixtureId = fixture.id)}
                >
                    <td
                        class:text-3xl={hoveringFixtureId === fixture.id}
                        class="transition-all"
                    >
                        <div class="mt-2 flex space-x-2">
                            <p class="mt-2">
                                {fixture.name}
                            </p>
                        </div></td
                    >
                </tr>
            {/each}
        </tbody>
    </table>
</div>
