<script lang="ts">
    import P5jssketch from "$lib/molecules/p5jssketch.svelte";
    import { type DmxFixtureFileDescriptor } from "$lib/types/bindings/DmxFixtureFileDescriptor";
    import { fixtures } from "$lib/stores/fixtures";
    import { WIDTH, HEIGHT } from "../+page.svelte";
    import { type FixtureGroup } from "$lib/types/bindings/FixtureGroup";

    interface InternalFixture {
        fixture: DmxFixtureFileDescriptor;
        moving: boolean;
    }

    let {
        selectedGroup,
        onchange,
    }: {
        selectedGroup: FixtureGroup | undefined;
        onchange: () => any;
    } = $props();

    let internalFixtures: InternalFixture[] = $state([]);

    $effect(() => {
        internalFixtures = $fixtures.map((fixture) => {
            return {
                fixture,
                moving: false,
            };
        });
    });

    const sketch = (p5: any) => {
        p5.setup = () => {};

        p5.draw = () => {
            p5.createCanvas(WIDTH, HEIGHT);
            p5.background(220);

            internalFixtures.forEach((fixture) => {
                if (
                    selectedGroup !== undefined &&
                    selectedGroup.fixture_ids
                        .map((fxId) =>
                            $fixtures.find((fixture) => fixture.id === fxId),
                        )
                        .find(
                            (groupFixture) =>
                                groupFixture &&
                                groupFixture.id === fixture.fixture.id,
                        )
                ) {
                    p5.fill(255, 150, 0);
                } else {
                    p5.fill(0);
                }

                p5.circle(
                    fixture.fixture.stage_coordinates[0],
                    fixture.fixture.stage_coordinates[1],
                    25,
                );

                p5.textAlign(p5.CENTER);
                p5.text(
                    fixture.fixture.name,
                    fixture.fixture.stage_coordinates[0],
                    fixture.fixture.stage_coordinates[1] + 25,
                );
            });
        };

        p5.mousePressed = () => {
            let movableFixture = internalFixtures.find(
                (fixture) =>
                    p5.dist(
                        fixture.fixture.stage_coordinates[0],
                        fixture.fixture.stage_coordinates[1],
                        p5.mouseX,
                        p5.mouseY,
                    ) < 26,
            );
            if (movableFixture) {
                movableFixture.moving = true;
            }
        };

        p5.doubleClicked = () => {
            let clickedFixture = internalFixtures.find(
                (fixture) =>
                    p5.dist(
                        fixture.fixture.stage_coordinates[0],
                        fixture.fixture.stage_coordinates[1],
                        p5.mouseX,
                        p5.mouseY,
                    ) < 26,
            );
            if (
                clickedFixture &&
                selectedGroup &&
                selectedGroup.id !== null &&
                clickedFixture.fixture.id !== null
            ) {
                if (
                    selectedGroup.fixture_ids.find(
                        (groupFixtureId) =>
                            groupFixtureId === clickedFixture.fixture.id,
                    )
                ) {
                    if (clickedFixture && selectedGroup.fixture_ids) {
                        selectedGroup.fixture_ids =
                            selectedGroup.fixture_ids.filter(
                                (fxId) => clickedFixture.fixture.id !== fxId,
                            );
                    }
                } else {
                    if (clickedFixture && selectedGroup.fixture_ids) {
                        selectedGroup.fixture_ids.push(
                            clickedFixture.fixture.id,
                        );
                    }
                }
                onchange();
            }
        };

        p5.mouseReleased = async () => {
            let hasMovingFixtures = false;
            for (const fixture of internalFixtures) {
                if (fixture.moving) {
                    hasMovingFixtures = true;
                    fixture.fixture.stage_coordinates[0] = Math.round(
                        fixture.fixture.stage_coordinates[0],
                    );
                    fixture.fixture.stage_coordinates[1] = Math.round(
                        fixture.fixture.stage_coordinates[1],
                    );

                    fixtures.updateLocal((storeFxs) => {
                        storeFxs.forEach((storeFx) => {
                            if (storeFx.id === fixture.fixture.id) {
                                storeFx.stage_coordinates =
                                    fixture.fixture.stage_coordinates;
                            }
                        });
                        return storeFxs;
                    });
                }
            }
            if (hasMovingFixtures) {
                await fixtures.updateRemote();
            }
            internalFixtures.forEach((fixture) => (fixture.moving = false));
        };

        p5.mouseMoved = () => {
            let movableFixture = internalFixtures.find(
                (fixture) =>
                    p5.dist(
                        fixture.fixture.stage_coordinates[0],
                        fixture.fixture.stage_coordinates[1],
                        p5.mouseX,
                        p5.mouseY,
                    ) < 12,
            );
            if (movableFixture) {
                p5.cursor(p5.MOVE);
            } else {
                p5.cursor(p5.ARROW);
            }
        };

        p5.mouseDragged = () => {
            let movableFixture = internalFixtures.find(
                (fixture) => fixture.moving,
            );
            if (movableFixture) {
                movableFixture.fixture.stage_coordinates[0] = p5.mouseX;
                movableFixture.fixture.stage_coordinates[1] = p5.mouseY;
            }
        };
    };
</script>

<svelte:window />

<P5jssketch {sketch} />
